// Electron API 类型定义

export interface ElectronAPI {
    // 交易系统相关
    tradingSystem: {
        getStatus: () => Promise<ApiResponse<TradingSystemStatus>>;
        initialize: (args?: any) => Promise<ApiResponse>;
        shutdown: () => Promise<ApiResponse>;
        getMarketStatus: () => Promise<ApiResponse>;
        getTradingStatus: () => Promise<ApiResponse>;
    };
    // 任务管理相关
    task: {
        create: (taskConfig: TaskCreateOptions) => Promise<ApiResponse<{ taskId: string }>>;
        start: (args: { taskId: string }) => Promise<ApiResponse<{ taskId: string }>>;
        stop: (args: { taskId: string }) => Promise<ApiResponse<{ taskId: string }>>;
        delete: (args: { taskId: string }) => Promise<ApiResponse<{ taskId: string }>>;
        getList: () => Promise<ApiResponse<Task[]>>;
        getStatus: (args: { taskId: string }) => Promise<ApiResponse<{ taskId: string; status: TaskStatus }>>;
    };
    // 配置管理相关
    config: {
        get: (args: { section?: string }) => Promise<ApiResponse>;
        save: (args: { config: any }) => Promise<ApiResponse>;
        update: (args: { updates: any }) => Promise<ApiResponse>;
        getAll: () => Promise<ApiResponse>;
        reload: () => Promise<ApiResponse>;
        validate: (args: { config: any }) => Promise<ApiResponse>;
    };
    // 华盛通交易相关
    huasheng: {
        getAccountInfo: () => Promise<ApiResponse>;
        getFunds: () => Promise<ApiResponse>;
        connect: () => Promise<ApiResponse>;
        disconnect: () => Promise<ApiResponse>;
        getConnectionStatus: () => Promise<ApiResponse>;
    };
    // 窗口控制相关
    window: {
        minimize: () => void;
        maximize: () => void;
        close: () => void;
        toggleDevTools: () => void;
        reload: () => void;
    };
    // 右键菜单和编辑操作
    contextMenu: {
        showContextMenu: () => void;
        cut: () => void;
        copy: () => void;
        paste: () => void;
        selectAll: () => void;
        undo: () => void;
        redo: () => void;
    };
    // 事件监听
    on: (channel: string, callback: (event: any, ...args: any[]) => void) => void;
    removeListener: (channel: string, callback: (...args: any[]) => void) => void;
    removeAllListeners: (channel: string) => void;
}

declare global {
    interface Window {
        electronAPI: ElectronAPI;
    }
}

// 基础 API 响应类型
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data: T | null;
    error?: string;
}

// 交易系统状态
export interface TradingSystemStatus {
    initialized: boolean;
    marketConnected: boolean;
    tradingConnected: boolean;
    activeTasksCount: number;
    lastUpdate: number;
    error?: string;
    // 新增：ServiceManager状态信息
    services?: {
        marketData?: {
            status: "initializing" | "running" | "error" | "stopped" | "disabled";
            connected: boolean;
            enabled: boolean;
            error?: string;
            lastUpdate: number;
        };
        trading?: {
            status: "initializing" | "running" | "error" | "stopped" | "disabled";
            connected: boolean;
            enabled: boolean;
            error?: string;
            lastUpdate: number;
        };
    };
}

// 任务状态枚举
export enum TaskStatus {
    Stopped = "stopped",
    Running = "running",
    Paused = "paused",
    Error = "error",
    Liquidated = "liquidated"
}

// 策略类型枚举
export enum StrategyType {
    BigOrderMonitor = "strategy_a_big_order_monitor",
    BreakoutChase = "strategy_b_breakout_chase",
    MeanReversion = "strategy_e_mean_reversion",
    Momentum = "strategy_d_momentum",
    ValueInvesting = "strategy_f_value_investing",
    DividendCapture = "strategy_i_dividend_capture",
    PairsTrading = "strategy_j_pairs_trading",
    TrendFollowing = "strategy_k_trend_following"
}

// 市场枚举
export enum Market {
    HK = "HK",
    US = "US",
    SH = "SH",
    SZ = "SZ"
}

// 数据类型枚举
export enum DataType {
    Quote = "Quote",
    Ticker = "Ticker",
    OrderBook = "OrderBook",
    BrokerQueue = "BrokerQueue"
}

// 风险条件类型
export enum RiskConditionType {
    Price = "price",
    PnLRatio = "pnl_ratio",
    Behavior = "behavior",
    Time = "time",
    StopLoss = "stop_loss",
    TakeProfit = "take_profit",
    TrailingStop = "trailing_stop"
}

// 清仓策略类型
export enum LiquidationType {
    Market = "market",
    LimitOptimized = "limit_optimized",
    TWAP = "twap_vwap",
    SmartExecution = "smart_execution"
}

// 策略配置接口
export interface StrategyConfig {
    strategyType: StrategyType;
    params: Record<string, any>;
    requiredDataTypes: DataType[];
}

// 风险条件接口
export interface RiskCondition {
    id: string;
    type: RiskConditionType;
    params: Record<string, any>;
}

// 清仓策略接口
export interface LiquidationStrategy {
    type: LiquidationType;
    params: Record<string, any>;
}

// 风险配置接口
export interface RiskConfig {
    triggerLogic: "any" | "all";
    conditions: RiskCondition[];
    liquidationStrategy: LiquidationStrategy;
}

// 任务创建选项
export interface TaskCreateOptions {
    name: string;
    stockCode: string;
    stockName: string;
    market: Market;
    strategyConfig: StrategyConfig;
    riskConfig: RiskConfig;
    autoStart?: boolean;
}

// 任务接口
export interface Task {
    id: string;
    name: string;
    stockCode: string;
    stockName: string;
    market: Market;
    strategyName: string;
    status: TaskStatus;
    position: number;
    avgCost?: number;
    pnl: number;
    createdAt: Date;
    updatedAt: Date;
    strategyConfig: StrategyConfig;
    riskConfig: RiskConfig;
    isActive: boolean;
}

// 报价数据
export interface Quote {
    stockCode: string;
    price: number;
    open: number;
    high: number;
    low: number;
    prevClose: number;
    volume: number;
    amount: number;
    change: number;
    changePercent: number;
    timestamp: number;
}

// 逐笔成交
export interface Ticker {
    stockCode: string;
    price: number;
    volume: number;
    direction: "BUY" | "SELL" | "NEUTRAL";
    timestamp: number;
}

// 买卖盘
export interface OrderBook {
    stockCode: string;
    asks: OrderBookItem[];
    bids: OrderBookItem[];
    timestamp: number;
}

export interface OrderBookItem {
    price: number;
    volume: number;
    orderCount?: number;
}

// 经纪队列
export interface BrokerQueue {
    stockCode: string;
    askBrokers: BrokerItem[];
    bidBrokers: BrokerItem[];
    timestamp: number;
}

export interface BrokerItem {
    brokerId: string;
    brokerName: string;
    volume: number;
}

// 持仓信息
export interface Position {
    stockCode: string;
    quantity: number;
    avgCost: number;
    currentPrice: number;
    profit: number;
    profitPercent: number;
}

// 订单信息
export interface Order {
    orderId: string;
    stockCode: string;
    orderType: "BUY" | "SELL";
    price: number;
    quantity: number;
    filledQuantity: number;
    status: OrderStatus;
    createdAt: number;
    updatedAt: number;
}

// 订单状态
export enum OrderStatus {
    PENDING = "pending",
    SUBMITTED = "submitted",
    PARTIAL_FILLED = "partial_filled",
    FILLED = "filled",
    CANCELLED = "cancelled",
    REJECTED = "rejected"
}

// 账户信息
export interface Account {
    accountId: string;
    accountName: string;
    cash: number;
    marketValue: number;
    totalValue: number;
}
