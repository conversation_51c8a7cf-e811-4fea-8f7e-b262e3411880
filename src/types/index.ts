// 类型定义统一导出

// 从 communication 模块导入存在的类型
export type {
    ElectronRealtimeDataEvent,
    ConnectionStatusEvent,
    ErrorEvent
} from '../communication';

// 定义基础类型（原本应该从 communication 导入的类型）
export type ApiResponse<T = any> = {
    success: boolean;
    message: string;
    data: T | null;
    error?: string;
};

export type TradingDataType = 'Quote' | 'Ticker' | 'OrderBook' | 'BrokerQueue';

export type StockCode = string;

export interface TradingCommandParams {
    stockCode: string;
    command: string;
    params?: Record<string, any>;
}

export type AdapterStatus = 'disconnected' | 'connecting' | 'connected' | 'error';

export type ConnectionStatus = 'connected' | 'disconnected' | 'connecting' | 'error';

// 量化交易终端核心数据类型

// 任务状态枚举
export type TaskStatus = 'running' | 'paused' | 'error' | 'stopped' | 'liquidated';

// 策略任务
export interface Task {
    id: string;
    name: string; // 任务名称，如"腾讯大单策略"
    stockCode: string; // 股票代码，如"HK.00700"
    stockName: string; // 股票名称，如"腾讯控股"
    strategyName: string; // 策略名称，如"策略A: 大单监控"
    status: TaskStatus;
    position: number; // 持仓数量，0表示空仓
    avgCost?: number; // 平均成本价
    pnl: number; // 浮动盈亏（港币）
    createdAt: Date;
    updatedAt: Date;
    strategyConfig: StrategyConfig;
    riskConfig: RiskConfig;
}

// 策略配置基础接口
export interface StrategyConfig {
    strategyType: string; // 策略类型标识
    params: Record<string, any>; // 策略参数，根据策略类型动态变化
    sellStrategy?: SellStrategy; // 可选的正常卖出策略配置
}

// 策略A：大单监控配置
export interface StrategyAConfig extends StrategyConfig {
    strategyType: 'strategy_a_big_order_monitor';
    params: {
        monitorThreshold: number; // 监控买盘阈值(股)
        durationSeconds: number; // 持续时间(秒)
        targetBrokers: string[]; // 目标经纪商列表
        orderSize: number; // 单笔买入股数
    };
}

// 策略B：突破追涨配置
export interface StrategyBConfig extends StrategyConfig {
    strategyType: 'strategy_b_breakout_chase';
    params: {
        breakoutPeriod: number; // 突破周期(日)
        volumeMultiplier: number; // 成交量放大倍数
        pullbackPercent: number; // 回踩幅度(%)
    };
}

// 风险管理配置
export interface RiskConfig {
    triggerLogic: 'any' | 'all'; // 触发逻辑：满足任一条件或全部条件
    conditions: RiskCondition[]; // 风险条件列表
    liquidationStrategy: LiquidationStrategy; // 清仓执行策略
}

// 风险条件
export interface RiskCondition {
    id: string;
    type: 'price' | 'pnl_ratio' | 'behavior' | 'time' | 
          'fast_stop_loss' | 'fast_take_profit' | 'time_limit' | 'price_deviation' |
          'volume_shrinkage' | 'volatility' | 'orderbook_anomaly' | 'trailing_stop' |
          'time_based_risk' | 'consecutive_loss_circuit_breaker'; // 条件类型
    params: Record<string, any>; // 条件参数
}

// 价格条件
export interface PriceRiskCondition extends RiskCondition {
    type: 'price';
    params: {
        trigger: 'touch' | 'break'; // 触及或突破
        direction: 'above' | 'below'; // 高于或低于
        price: number; // 目标价格
    };
}

// 盈亏比例条件
export interface PnLRatioRiskCondition extends RiskCondition {
    type: 'pnl_ratio';
    params: {
        lossRatio: number; // 亏损比例(%)
    };
}

// 行为条件
export interface BehaviorRiskCondition extends RiskCondition {
    type: 'behavior';
    params: {
        timePeriod: number; // 时间周期
        timeUnit: 'seconds' | 'minutes' | 'hours'; // 时间单位
        targetGroup: string; // 监测对象（经纪商分组）
        behaviorType: 'buy' | 'sell'; // 监测行为类型
        volumeThreshold: number; // 成交量阈值
    };
}

// 时间条件
export interface TimeRiskCondition extends RiskCondition {
    type: 'time';
    params: {
        holdingMinutes: number; // 持仓超过N分钟
    };
}

// 清仓执行策略
export interface LiquidationStrategy {
    type: 'market' | 'limit_optimized' | 'twap_vwap' | 'immediate_market' | 'smart_execution' | 'sliced_execution'; // 清仓方式
    params: Record<string, any>; // 策略参数
}

// 市价单清仓
export interface MarketLiquidationStrategy extends LiquidationStrategy {
    type: 'market';
    params: Record<string, never>; // 市价单无需额外参数
}

// 优化限价单清仓
export interface LimitOptimizedLiquidationStrategy extends LiquidationStrategy {
    type: 'limit_optimized';
    params: {
        basePrice: 'bid1' | 'ask1' | 'last'; // 基础价格
        priceOffset: number; // 价格偏移(ticks)
        direction: 'up' | 'down'; // 偏移方向
        timeoutSeconds: number; // 超时时间(秒)
        timeoutAction: 'cancel_and_market' | 'cancel_only'; // 超时后动作
    };
}

// TWAP/VWAP算法单清仓
export interface AlgoLiquidationStrategy extends LiquidationStrategy {
    type: 'twap_vwap';
    params: {
        executionMinutes: number; // 执行时间(分钟)
    };
}

// 全局状态
export interface GlobalStatus {
    totalTasks: number; // 总任务数
    runningTasks: number; // 运行中任务数
    totalMarketValue: number; // 总持仓市值(港币)
    totalPnL: number; // 总浮动盈亏(港币)
    connections: {
        market: ConnectionStatus; // 行情连接状态
        trading: ConnectionStatus; // 交易连接状态
    };
}

// 策略模板定义
export interface StrategyTemplate {
    id: string;
    name: string; // 显示名称，如"策略A: 大单监控"
    description: string; // 策略描述
    configSchema: StrategyConfigField[]; // 配置表单字段定义
    defaultConfig: Record<string, any>; // 默认配置值
}

// 策略配置字段定义
export interface StrategyConfigField {
    name: string; // 字段名
    label: string; // 显示标签
    type: 'number' | 'text' | 'select' | 'multiselect' | 'checkbox'; // 字段类型
    required?: boolean; // 是否必填
    defaultValue?: any; // 默认值
    options?: { label: string; value: any }[]; // 选项（用于select/multiselect）
    placeholder?: string; // 占位符文本
    validation?: {
        min?: number;
        max?: number;
        step?: number;
        pattern?: string;
    };
    helpText?: string; // 帮助说明
}

// 风控条件模板定义
export interface RiskConditionTemplate {
    id: string;
    name: string; // 显示名称，如"价格触及"
    description: string; // 条件描述
    type: RiskCondition['type']; // 条件类型
    configFields: StrategyConfigField[]; // 配置字段
    defaultParams: Record<string, any>; // 默认参数
}

// 清仓策略模板定义
export interface LiquidationTemplate {
    id: string;
    name: string; // 显示名称，如"市价单"
    description: string; // 策略描述
    type: LiquidationStrategy['type']; // 策略类型
    configFields: StrategyConfigField[]; // 配置字段
    defaultParams: Record<string, any>; // 默认参数
}

// 模拟数据类型（用于原型开发）
export interface MockOrderBookEntry {
    price: number;
    volume: number;
    brokerIds: string[];
}

export interface MockOrderBook {
    stockCode: string;
    bids: MockOrderBookEntry[]; // 买盘
    asks: MockOrderBookEntry[]; // 卖盘
    timestamp: Date;
}

export interface MockTickData {
    stockCode: string;
    price: number;
    volume: number;
    direction: 'buy' | 'sell' | 'neutral';
    timestamp: Date;
    brokerId?: string;
}

export interface MockBrokerQueueEntry {
    brokerId: string;
    brokerName: string;
    volume: number;
    orders: number;
}

export interface MockBrokerQueue {
    stockCode: string;
    side: 'buy' | 'sell';
    priceLevel: number;
    brokers: MockBrokerQueueEntry[];
    timestamp: Date;
}

// 经纪商管理类型定义
export interface Broker {
    id: string;
    name: string; // 显示名称，如"高盛"
    code: string; // 代码标识，如"goldman_sachs"
    createdAt: Date;
}

export interface BrokerManagerState {
    brokers: Broker[];
    loading: boolean;
    error?: string;
}

// 日志类型定义
export type LogLevel = 'info' | 'warning' | 'error' | 'debug';
export type LogCategory = 'strategy' | 'trading' | 'system';

export interface LogEntry {
    id: string;
    timestamp: Date;
    level: LogLevel;
    category: LogCategory;
    taskId?: string; // 关联的任务ID
    message: string;
    details?: Record<string, any>; // 额外详情
}

// ========================= T0 短期交易风控类型定义 =========================

// T0风控条件基础接口
export interface T0RiskCondition extends RiskCondition {
    priority: 'low' | 'medium' | 'high' | 'critical'; // 优先级
    enabled: boolean; // 是否启用
    responseTimeMs: number; // 响应时间要求（毫秒）
    lastChecked?: Date; // 最后检查时间
    triggerCount: number; // 触发次数统计
}

// 快速止损条件
export interface FastStopLossCondition extends RiskCondition {
    type: 'fast_stop_loss';
    params: {
        lossPercentage: number; // 止损比例 1-10%
        basedOnBuyPrice: boolean; // 基于买入价计算
        responseTimeMs: number; // 响应时间要求
    };
}

// 快速止盈条件
export interface FastTakeProfitCondition extends RiskCondition {
    type: 'fast_take_profit';
    params: {
        profitPercentage: number; // 止盈比例 0.5-20%
        basedOnBuyPrice: boolean; // 基于买入价计算
        enableTrailing: boolean; // 是否启用移动止盈
        trailingPercentage?: number; // 移动止盈回撤比例
    };
}

// 时间限制条件
export interface TimeLimitCondition extends RiskCondition {
    type: 'time_limit';
    params: {
        maxHoldingMinutes: number; // 最大持仓时间 5-240分钟
        warningMinutesBefore: number; // 提前预警时间
        forceExecution: boolean; // 是否强制执行
    };
}

// 价格偏离度条件
export interface PriceDeviationCondition extends RiskCondition {
    type: 'price_deviation';
    params: {
        upwardDeviationPercent: number; // 向上偏离阈值
        downwardDeviationPercent: number; // 向下偏离阈值
        deviationMethod: 'percentage' | 'ticks'; // 计算方式
        tickSize?: number; // tick大小（当使用ticks方式时）
    };
}

// 成交量萎缩条件
export interface VolumeShrinkageCondition extends RiskCondition {
    type: 'volume_shrinkage';
    params: {
        timeWindowMinutes: number; // 监控时间窗口 1-30分钟
        shrinkageRatio: number; // 萎缩比例阈值 20-80%
        comparisonPeriod: 'session_avg' | 'recent_avg'; // 比较基准
        minimumVolume: number; // 最小成交量要求
    };
}

// 市场波动率条件
export interface VolatilityCondition extends RiskCondition {
    type: 'volatility';
    params: {
        calculationPeriodMinutes: number; // 计算周期 5-30分钟
        volatilityMethod: 'std_dev' | 'atr'; // 计算方法
        threshold: number; // 波动率阈值
        triggerDirection: 'high' | 'low' | 'both'; // 触发方向
    };
}

// 盘口异常条件
export interface OrderBookAnomalyCondition extends RiskCondition {
    type: 'orderbook_anomaly';
    params: {
        spreadThresholdPercent: number; // 价差异常阈值
        depthThresholdPercent: number; // 深度不足阈值
        levelCount: number; // 监控档数
        anomalyDurationSeconds: number; // 异常持续时间
    };
}

// 追踪止损条件
export interface TrailingStopCondition extends RiskCondition {
    type: 'trailing_stop';
    params: {
        trailingPercent: number; // 追踪回撤比例 0.5-5%
        activationProfitPercent: number; // 激活止损的盈利比例
        updateFrequencyMs: number; // 更新频率（毫秒）
        highestPrice?: number; // 当前最高价（运行时）
        currentStopPrice?: number; // 当前止损价（运行时）
    };
}

// 分时段风控条件
export interface TimeBasedRiskCondition extends RiskCondition {
    type: 'time_based_risk';
    params: {
        timeRules: Array<{
            startTime: string; // 开始时间 "09:30"
            endTime: string; // 结束时间 "10:00"
            riskProfile: 'conservative' | 'normal' | 'aggressive'; // 风险配置
            customSettings?: Partial<T0RiskCondition>; // 自定义设置
        }>;
        autoForceCloseBefore: string; // 强制清仓时间 "15:30"
    };
}

// 连续亏损熔断条件
export interface ConsecutiveLossCircuitBreakerCondition extends RiskCondition {
    type: 'consecutive_loss_circuit_breaker';
    params: {
        maxConsecutiveLosses: number; // 最大连续亏损次数 2-10
        lossThresholdPercent: number; // 亏损认定阈值
        circuitBreakerDurationMinutes: number; // 熔断持续时间
        autoResetEnabled: boolean; // 是否自动解除熔断
        currentLossStreak: number; // 当前连续亏损次数（运行时）
        lastLossTime?: Date; // 最后亏损时间（运行时）
    };
}

// 风控上下文数据
export interface RiskCheckContext {
    taskId: string;
    currentPrice: number;
    buyPrice: number;
    position: number;
    holdingTimeMinutes: number;
    currentPnL: number;
    currentPnLPercent: number;
    marketData: {
        timestamp: Date;
        volume: number;
        volatility: number;
        orderBook: OrderBookSnapshot;
        recentPrices: number[]; // 最近价格序列
        recentVolumes: number[]; // 最近成交量序列
    };
    sessionData: {
        sessionStartTime: Date;
        totalTrades: number;
        consecutiveLosses: number;
        todayPnL: number;
    };
}

export interface OrderBookSnapshot {
    bids: Array<{ price: number; volume: number }>;
    asks: Array<{ price: number; volume: number }>;
    spread: number;
    spreadPercent: number;
    depth: { bidDepth: number; askDepth: number };
}

// ========================= 正常卖出策略类型定义 =========================

// 卖出策略基础接口
export interface SellStrategy {
    enabled: boolean; // 是否启用卖出策略
    conditions: SellCondition[]; // 卖出触发条件列表
    execution: SellExecution; // 卖出执行方式
}

// 卖出触发条件基础接口
export interface SellCondition {
    id: string;
    type: 'fixed_volume' | 'dynamic_percentage' | 'price_target' | 'time_based'; // 条件类型
    params: Record<string, any>; // 条件参数
}

// 固定成交量触发条件
export interface FixedVolumeSellCondition extends SellCondition {
    type: 'fixed_volume';
    params: {
        volumeThreshold: number; // 触发成交量阈值
        direction: 'buy' | 'sell' | 'both'; // 监控方向
        brokerFilter?: string[]; // 可选的经纪商筛选
    };
}

// 动态百分比触发条件（基于买入单量）
export interface DynamicPercentageSellCondition extends SellCondition {
    type: 'dynamic_percentage';
    params: {
        baseVolumeSource: 'buy_trigger_volume'; // 基准成交量来源
        percentage: number; // 百分比 (0-100)
        direction: 'buy' | 'sell' | 'both'; // 监控方向
        brokerFilter?: string[]; // 可选的经纪商筛选
    };
}

// 价格目标触发条件
export interface PriceTargetSellCondition extends SellCondition {
    type: 'price_target';
    params: {
        targetPrice: number; // 目标价格
        triggerType: 'touch' | 'break'; // 触及或突破
        direction: 'above' | 'below'; // 高于或低于
    };
}

// 时间基础触发条件
export interface TimeBasedSellCondition extends SellCondition {
    type: 'time_based';
    params: {
        holdingMinutes: number; // 持仓时间（分钟）
        autoTrigger: boolean; // 是否自动触发
    };
}

// 卖出执行方式基础接口
export interface SellExecution {
    type: 'priority_levels' | 'market_order' | 'limit_order' | 'smart_execution'; // 执行类型
    params: Record<string, any>; // 执行参数
    fallbackStrategy?: SellExecution; // 失败时的备用策略
}

// 优先档位执行方式
export interface PriorityLevelsSellExecution extends SellExecution {
    type: 'priority_levels';
    params: {
        basePriceSource: 'trigger_order_price' | 'current_ask' | 'current_bid'; // 基准价格来源
        levelCount: number; // 尝试档位数量 (1-5)
        tickDirection: 'up' | 'down'; // 档位方向
        levelTimeout: number; // 每档位超时时间（秒）
        onAllLevelsFailed: 'alert_manual' | 'market_order' | 'risk_control'; // 所有档位失败时的处理方式
    };
}

// 卖出策略模板定义
export interface SellStrategyTemplate {
    id: string;
    name: string; // 显示名称
    description: string; // 策略描述
    conditionTemplates: SellConditionTemplate[]; // 支持的条件模板
    executionTemplates: SellExecutionTemplate[]; // 支持的执行模板
    defaultConfig: {
        enabled: boolean;
        conditions: Array<{ type: string; params: Record<string, any> }>;
        execution: { type: string; params: Record<string, any> };
    };
}

// 卖出条件模板定义
export interface SellConditionTemplate {
    id: string;
    name: string;
    description: string;
    type: SellCondition['type'];
    configFields: StrategyConfigField[];
    defaultParams: Record<string, any>;
}

// 卖出执行模板定义
export interface SellExecutionTemplate {
    id: string;
    name: string;
    description: string;
    type: SellExecution['type'];
    configFields: StrategyConfigField[];
    defaultParams: Record<string, any>;
}
