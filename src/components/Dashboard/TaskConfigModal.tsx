import React, { useState, useEffect } from 'react';
import { Task, StrategyTemplate, RiskConditionTemplate, LiquidationTemplate, SellStrategyTemplate, SellConditionTemplate, SellExecutionTemplate } from '../../types';
import { STRATEGY_TEMPLATES, RISK_CONDITION_TEMPLATES, LIQUIDATION_TEMPLATES, SELL_STRATEGY_TEMPLATES, SELL_CONDITION_TEMPLATES, SELL_EXECUTION_TEMPLATES } from '../../data/strategyTemplates';
import { MultiSelectDropdown } from '../MultiSelectDropdown';
import { BrokerManagerModal } from './BrokerManagerModal';
import { brokerManager } from '../../services/brokerManager';
import { useModalOverlay } from '@/hooks/useModalOverlay';
import '../MultiSelectDropdown.css';

interface TaskConfigModalProps {
    task?: Task | null;
    onSave: (taskConfig: Partial<Task>) => void;
    onClose: () => void;
}

type TabType = 'basic' | 'strategy' | 'risk';

export const TaskConfigModal: React.FC<TaskConfigModalProps> = ({
    task,
    onSave,
    onClose
}) => {
    // 标签页状态
    const [activeTab, setActiveTab] = useState<TabType>('basic');
    
    // 经纪商管理状态
    const [isBrokerManagerOpen, setIsBrokerManagerOpen] = useState(false);
    const [brokerOptionsVersion, setBrokerOptionsVersion] = useState(0); // 用于强制刷新经纪商选项

    // 使用更严格的遮罩层点击处理（防止拖拽选择时意外关闭）
    const { handleOverlayClick, handleOverlayMouseDown } = useModalOverlay(() => {
        // 如果有经纪商管理弹框打开，不关闭主弹框
        if (!isBrokerManagerOpen) {
            onClose();
        }
    });
    
    // 表单数据状态
    const [formData, setFormData] = useState({
        // 基本信息
        name: '',
        stockCode: '',
        stockName: '',
        selectedStrategyId: '',
        
        // 策略配置
        strategyParams: {} as Record<string, any>,
        
        // 卖出策略配置
        sellStrategyEnabled: false,
        sellConditions: [] as any[],
        sellExecutionType: 'priority_levels',
        sellExecutionParams: {} as Record<string, any>,
        
        // 风控配置
        riskTriggerLogic: 'any' as 'any' | 'all',
        riskConditions: [] as any[],
        liquidationStrategyId: 'market_order',
        liquidationParams: {} as Record<string, any>
    });

    // 初始化表单数据
    useEffect(() => {
        if (task && task.strategyConfig && task.riskConfig) {
            setFormData({
                name: task.name,
                stockCode: task.stockCode,
                stockName: task.stockName,
                selectedStrategyId: task.strategyConfig.strategyType,
                strategyParams: task.strategyConfig.params || {},
                sellStrategyEnabled: task.strategyConfig.sellStrategy?.enabled || false,
                sellConditions: task.strategyConfig.sellStrategy?.conditions || [],
                sellExecutionType: task.strategyConfig.sellStrategy?.execution.type || 'priority_levels',
                sellExecutionParams: task.strategyConfig.sellStrategy?.execution.params || {},
                riskTriggerLogic: task.riskConfig.triggerLogic,
                riskConditions: task.riskConfig.conditions || [],
                liquidationStrategyId: task.riskConfig.liquidationStrategy?.type || '',
                liquidationParams: task.riskConfig.liquidationStrategy?.params || {}
            });
        } else {
            // 新建任务时的默认值
            setFormData({
                name: '',
                stockCode: '',
                stockName: '',
                selectedStrategyId: STRATEGY_TEMPLATES[0]?.id || '',
                strategyParams: {},
                sellStrategyEnabled: false,
                sellConditions: [],
                sellExecutionType: 'priority_levels',
                sellExecutionParams: {},
                riskTriggerLogic: 'any',
                riskConditions: [],
                liquidationStrategyId: 'immediate_market',
                liquidationParams: {}
            });
        }
    }, [task]);

    // 获取当前选中的策略模板
    const selectedStrategy = STRATEGY_TEMPLATES.find(s => s.id === formData.selectedStrategyId);
    
    // 获取当前选中的清仓策略模板
    const selectedLiquidation = LIQUIDATION_TEMPLATES.find(l => l.id === formData.liquidationStrategyId);
    
    // 获取当前选中的卖出执行模板
    const selectedSellExecution = SELL_EXECUTION_TEMPLATES.find(e => e.id === formData.sellExecutionType);
    
    // 检查当前策略是否需要卖出策略
    const strategyRequiresSell = selectedStrategy?.defaultConfig?.requiresSellStrategy || false;

    // 处理经纪商列表更新
    const handleBrokerListUpdate = () => {
        setBrokerOptionsVersion(prev => prev + 1); // 触发重新渲染
    };

    // 获取当前的经纪商选项 (响应式更新)
    const getCurrentBrokerOptions = () => {
        return brokerManager.getBrokerOptions();
    };

    // 更新表单字段
    const updateField = (field: string, value: any) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // 更新策略参数
    const updateStrategyParam = (paramName: string, value: any) => {
        setFormData(prev => ({
            ...prev,
            strategyParams: {
                ...prev.strategyParams,
                [paramName]: value
            }
        }));
    };

    // 更新清仓参数
    const updateLiquidationParam = (paramName: string, value: any) => {
        setFormData(prev => ({
            ...prev,
            liquidationParams: {
                ...prev.liquidationParams,
                [paramName]: value
            }
        }));
    };

    // 添加风控条件
    const addRiskCondition = (templateId: string) => {
        const template = RISK_CONDITION_TEMPLATES.find(t => t.id === templateId);
        if (template) {
            // 检查是否已存在相同类型的条件
            const existingCondition = formData.riskConditions.find(condition => condition.type === template.type);
            if (existingCondition) {
                alert(`已存在"${template.name}"条件，每种类型只能添加一个。`);
                return;
            }
            
            const newCondition = {
                id: `risk_${Date.now()}`,
                type: template.type,
                params: { ...template.defaultParams }
            };
            setFormData(prev => ({
                ...prev,
                riskConditions: [...prev.riskConditions, newCondition]
            }));
        }
    };

    // 删除风控条件
    const removeRiskCondition = (conditionId: string) => {
        setFormData(prev => ({
            ...prev,
            riskConditions: prev.riskConditions.filter(c => c.id !== conditionId)
        }));
    };

    // 更新风控条件参数
    const updateRiskConditionParam = (conditionId: string, paramName: string, value: any) => {
        setFormData(prev => ({
            ...prev,
            riskConditions: prev.riskConditions.map(condition => 
                condition.id === conditionId 
                    ? {
                        ...condition,
                        params: {
                            ...condition.params,
                            [paramName]: value
                        }
                    }
                    : condition
            )
        }));
    };

    // 处理策略选择变化
    const handleStrategyChange = (strategyId: string) => {
        const strategy = STRATEGY_TEMPLATES.find(s => s.id === strategyId);
        updateField('selectedStrategyId', strategyId);
        if (strategy) {
            updateField('strategyParams', { ...strategy.defaultConfig });
        }
    };

    // 处理清仓策略变化
    const handleLiquidationChange = (liquidationId: string) => {
        const liquidation = LIQUIDATION_TEMPLATES.find(l => l.id === liquidationId);
        updateField('liquidationStrategyId', liquidationId);
        if (liquidation) {
            updateField('liquidationParams', { ...liquidation.defaultParams });
        }
    };

    // 保存任务配置
    const handleSave = () => {
        if (!formData.name || !formData.stockCode || !formData.selectedStrategyId) {
            alert('请填写必填项');
            return;
        }

        // 检查策略是否要求卖出策略
        if (strategyRequiresSell && !formData.sellStrategyEnabled) {
            alert('此策略必须配置卖出策略');
            return;
        }

        const taskConfig: Partial<Task> = {
            name: formData.name,
            stockCode: formData.stockCode,
            stockName: formData.stockName || formData.stockCode,
            strategyName: selectedStrategy?.name || '',
            strategyConfig: {
                strategyType: formData.selectedStrategyId,
                params: formData.strategyParams,
                sellStrategy: formData.sellStrategyEnabled ? {
                    enabled: formData.sellStrategyEnabled,
                    conditions: formData.sellConditions,
                    execution: {
                        type: formData.sellExecutionType as any,
                        params: formData.sellExecutionParams
                    }
                } : undefined
            },
            riskConfig: {
                triggerLogic: formData.riskTriggerLogic,
                conditions: formData.riskConditions,
                liquidationStrategy: {
                    type: formData.liquidationStrategyId as any,
                    params: formData.liquidationParams
                }
            }
        };

        onSave(taskConfig);
    };

    // 验证当前标签页是否可以切换
    const canSwitchTab = (targetTab: TabType): boolean => {
        // 基本信息和策略选择选项卡总是可以切换
        if (targetTab === 'basic' || targetTab === 'strategy') {
            return true;
        }
        // 风控配置需要先选择策略
        if (targetTab === 'risk' && !formData.selectedStrategyId) {
            return false;
        }
        return true;
    };

    // 标签页点击处理
    const handleTabClick = (tab: TabType) => {
        if (canSwitchTab(tab)) {
            setActiveTab(tab);
        }
    };

    return (
        <div
            className="modal-overlay"
            onMouseDown={handleOverlayMouseDown}
            onClick={handleOverlayClick}
        >
            <div className="modal-content task-config-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>{task ? '编辑任务' : '添加新任务'}</h2>
                    <button className="modal-close" onClick={onClose}>×</button>
                </div>
                
                {/* 标签页导航 */}
                <div className="modal-tabs">
                    <button 
                        className={`tab-button ${activeTab === 'basic' ? 'active' : ''}`}
                        onClick={() => handleTabClick('basic')}
                    >
                        基本信息
                    </button>
                    <button 
                        className={`tab-button ${activeTab === 'strategy' ? 'active' : ''}`}
                        onClick={() => handleTabClick('strategy')}
                    >
                        策略选择与条件配置
                    </button>
                    <button 
                        className={`tab-button ${activeTab === 'risk' ? 'active' : ''} ${!canSwitchTab('risk') ? 'disabled' : ''}`}
                        onClick={() => handleTabClick('risk')}
                        disabled={!canSwitchTab('risk')}
                    >
                        应急风控配置
                    </button>
                </div>

                <div className="modal-body">
                    {/* 基本信息标签页 */}
                    {activeTab === 'basic' && (
                        <div className="tab-content">
                            <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '15px' }}>
                                <div style={{ flex: 1 }}>
                                    <h4 style={{ margin: 0, display: 'flex', alignItems: 'center', color: '#495057' }}>
                                        任务基本信息
                                        <span className="required-asterisk" style={{ marginLeft: '5px' }}> *</span>
                                    </h4>
                                    <p style={{ color: '#666', fontSize: '13px', margin: '5px 0 0 0', lineHeight: '1.4' }}>
                                        配置任务的基本信息，包括任务名称和目标股票，这些信息将用于识别和管理交易任务
                                    </p>
                                </div>
                            </div>

                            <div style={{ 
                                background: '#f8f9fa', 
                                border: '1px solid #e9ecef', 
                                borderRadius: '6px', 
                                padding: '20px' 
                            }}>
                                <div className="form-group">
                                    <label style={{ fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
                                        任务名称
                                        <span className="required-asterisk"> *</span>
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.name}
                                        onChange={(e) => updateField('name', e.target.value)}
                                        placeholder="如：腾讯大单策略"
                                        style={{ width: '100%' }}
                                    />
                                    <small className="field-help">为任务设置一个易于识别的名称</small>
                                </div>

                                <div className="form-group">
                                    <label style={{ fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
                                        目标股票
                                        <span className="required-asterisk"> *</span>
                                    </label>
                                    <div className="stock-input-group">
                                        <input
                                            type="text"
                                            value={formData.stockCode}
                                            onChange={(e) => updateField('stockCode', e.target.value)}
                                            placeholder="如：HK.00700"
                                            style={{ flex: '1', marginRight: '10px' }}
                                        />
                                        <input
                                            type="text"
                                            value={formData.stockName}
                                            onChange={(e) => updateField('stockName', e.target.value)}
                                            placeholder="股票名称（可选）"
                                            style={{ flex: '1' }}
                                        />
                                    </div>
                                    <small className="field-help">输入完整的股票代码和名称，如 HK.00700 腾讯控股</small>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* 策略选择与条件配置标签页 */}
                    {activeTab === 'strategy' && (
                        <div className="tab-content">
                            {/* 买入策略配置部分 */}
                            <div className="buy-strategy-section">
                                <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '15px' }}>
                                    <div style={{ flex: 1 }}>
                                        <h4 style={{ margin: 0, display: 'flex', alignItems: 'center', color: '#495057' }}>
                                            买入策略配置
                                            <span className="required-asterisk" style={{ marginLeft: '5px' }}> *</span>
                                        </h4>
                                        <p style={{ color: '#666', fontSize: '13px', margin: '5px 0 0 0', lineHeight: '1.4' }}>
                                            选择交易策略并配置买入触发条件，系统将根据这些条件自动执行买入操作
                                        </p>
                                    </div>
                                </div>

                                <div style={{ 
                                    background: '#f8f9fa', 
                                    border: '1px solid #e9ecef', 
                                    borderRadius: '6px', 
                                    padding: '20px' 
                                }}>
                                    {/* 策略选择 */}
                                    <div className="form-group">
                                        <label style={{ fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
                                            选择策略
                                            <span className="required-asterisk"> *</span>
                                        </label>
                                        <select
                                            value={formData.selectedStrategyId}
                                            onChange={(e) => handleStrategyChange(e.target.value)}
                                            style={{ width: '100%' }}
                                        >
                                            <option value="">请选择策略</option>
                                            {STRATEGY_TEMPLATES.map(strategy => (
                                                <option key={strategy.id} value={strategy.id}>
                                                    {strategy.name}
                                                </option>
                                            ))}
                                        </select>
                                        {!formData.selectedStrategyId && (
                                            <small className="field-help">请先选择一个交易策略以继续配置</small>
                                        )}
                                    </div>

                                    {/* 策略配置部分 */}
                                    {selectedStrategy && (
                                        <div style={{ marginTop: '20px' }}>
                                            <div style={{ 
                                                background: 'white', 
                                                border: '1px solid #dee2e6', 
                                                padding: '15px', 
                                                borderRadius: '4px',
                                                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                                                marginBottom: '15px'
                                            }}>
                                                <div style={{ marginBottom: '15px' }}>
                                                    <h5 style={{ margin: 0, fontWeight: 'bold', color: '#495057' }}>
                                                        {selectedStrategy.name}
                                                    </h5>
                                                    <div style={{ fontSize: '13px', color: '#6c757d', marginTop: '5px' }}>
                                                        {selectedStrategy.description}
                                                    </div>
                                                </div>

                                                {/* 策略参数配置 */}
                                                {selectedStrategy.configSchema.map(field => (
                                                    <div key={field.name} className="form-group">
                                                        {field.type !== 'multiselect' && (
                                                            <label>
                                                                {field.label}
                                                                {field.required && <span className="required-asterisk"> *</span>}
                                                            </label>
                                                        )}
                                                        
                                                        {field.type === 'number' && (
                                                            <input
                                                                type="number"
                                                                value={formData.strategyParams[field.name] || field.defaultValue || ''}
                                                                onChange={(e) => updateStrategyParam(field.name, parseFloat(e.target.value) || 0)}
                                                                placeholder={field.placeholder}
                                                                min={field.validation?.min}
                                                                max={field.validation?.max}
                                                                step={field.validation?.step || "any"}
                                                            />
                                                        )}
                                                        
                                                        {field.type === 'text' && (
                                                            <input
                                                                type="text"
                                                                value={formData.strategyParams[field.name] || field.defaultValue || ''}
                                                                onChange={(e) => updateStrategyParam(field.name, e.target.value)}
                                                                placeholder={field.placeholder}
                                                            />
                                                        )}
                                                        
                                                        {field.type === 'select' && (
                                                            <select
                                                                value={formData.strategyParams[field.name] || field.defaultValue || ''}
                                                                onChange={(e) => updateStrategyParam(field.name, e.target.value)}
                                                            >
                                                                {field.options?.map(option => (
                                                                    <option key={option.value} value={option.value}>
                                                                        {option.label}
                                                                    </option>
                                                                ))}
                                                            </select>
                                                        )}
                                                        
                                                        {field.type === 'multiselect' && (
                                                            <div>
                                                                {field.name === 'targetBrokers' && (
                                                                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                                                                        <label style={{ margin: 0, fontWeight: 'bold' }}>
                                                                            {field.label}
                                                                            {field.required && <span className="required-asterisk"> *</span>}
                                                                        </label>
                                                                        <button
                                                                            type="button"
                                                                            onClick={() => setIsBrokerManagerOpen(true)}
                                                                            style={{
                                                                                background: '#6c757d',
                                                                                color: 'white',
                                                                                border: 'none',
                                                                                padding: '4px 8px',
                                                                                borderRadius: '3px',
                                                                                cursor: 'pointer',
                                                                                fontSize: '11px',
                                                                                flexShrink: 0
                                                                            }}
                                                                        >
                                                                            管理经纪商
                                                                        </button>
                                                                    </div>
                                                                )}
                                                                {field.name !== 'targetBrokers' && (
                                                                    <label style={{ fontWeight: 'bold', marginBottom: '8px', display: 'block' }}>
                                                                        {field.label}
                                                                        {field.required && <span className="required-asterisk"> *</span>}
                                                                    </label>
                                                                )}
                                                                <MultiSelectDropdown
                                                                    options={field.name === 'targetBrokers' ? getCurrentBrokerOptions() : field.options || []}
                                                                    value={formData.strategyParams[field.name] || field.defaultValue || []}
                                                                    onChange={(selectedValues) => updateStrategyParam(field.name, selectedValues)}
                                                                    placeholder={field.placeholder || '请选择...'}
                                                                />
                                                            </div>
                                                        )}
                                                        
                                                        {field.helpText && field.name !== 'targetBrokers' && (
                                                            <small className="field-help">{field.helpText}</small>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}

                                    {!selectedStrategy && (
                                        <div style={{ 
                                            textAlign: 'center', 
                                            color: '#6c757d', 
                                            padding: '30px',
                                            background: 'white',
                                            border: '2px dashed #dee2e6',
                                            borderRadius: '6px',
                                            marginTop: '15px'
                                        }}>
                                            <div style={{ fontSize: '48px', marginBottom: '10px' }}>📋</div>
                                            <div style={{ fontWeight: '500' }}>请先选择一个交易策略</div>
                                            <div style={{ fontSize: '12px', marginTop: '5px', color: '#868e96' }}>选择策略后即可配置买入触发条件</div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* 卖出策略配置部分 */}
                            {selectedStrategy && (
                                <div className="sell-strategy-section" style={{ marginTop: '30px', borderTop: '1px solid #eee', paddingTop: '20px' }}>
                                    <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '15px' }}>
                                        <div style={{ flex: 1, marginRight: '20px' }}>
                                            <h4 style={{ margin: 0, display: 'flex', alignItems: 'center', color: '#495057' }}>
                                                卖出策略配置
                                                {strategyRequiresSell && <span className="required-asterisk" style={{ marginLeft: '5px' }}> *</span>}
                                            </h4>
                                            <p style={{ color: '#666', fontSize: '13px', margin: '5px 0 0 0', lineHeight: '1.4' }}>
                                                配置策略的正常卖出条件和执行方式，与应急风控不同，这是策略本身的主动卖出逻辑
                                            </p>
                                        </div>
                                        <div style={{ flexShrink: 0 }}>
                                            <label style={{ display: 'flex', alignItems: 'center', margin: 0, cursor: 'pointer', userSelect: 'none', whiteSpace: 'nowrap' }}>
                                                <input
                                                    type="checkbox"
                                                    checked={formData.sellStrategyEnabled}
                                                    onChange={(e) => updateField('sellStrategyEnabled', e.target.checked)}
                                                    style={{ marginRight: '8px' }}
                                                />
                                                <span style={{ 
                                                    fontWeight: formData.sellStrategyEnabled ? 'bold' : 'normal',
                                                    color: formData.sellStrategyEnabled ? '#2ed573' : '#666'
                                                }}>
                                                    {formData.sellStrategyEnabled ? '已启用' : '未启用'}
                                                </span>
                                            </label>
                                        </div>
                                    </div>

                                    {strategyRequiresSell && !formData.sellStrategyEnabled && (
                                        <div style={{ 
                                            background: '#fff3cd', 
                                            border: '1px solid #ffeaa7', 
                                            color: '#856404', 
                                            padding: '10px', 
                                            borderRadius: '4px', 
                                            marginBottom: '15px',
                                            fontSize: '14px'
                                        }}>
                                            ⚠️ 此策略必须配置卖出策略才能正常运行
                                        </div>
                                    )}

                                    {/* 卖出策略配置 */}
                                    {formData.sellStrategyEnabled ? (
                                        <div style={{ 
                                            background: '#f8f9fa', 
                                            border: '1px solid #e9ecef', 
                                            borderRadius: '6px', 
                                            padding: '20px' 
                                        }}>
                                            {/* 卖出触发条件 */}
                                            <div className="form-group">
                                                <label style={{ fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
                                                    卖出触发条件
                                                    <small style={{ fontWeight: 'normal', color: '#666', marginLeft: '8px' }}>
                                                        (可添加多个条件，满足任一即触发)
                                                    </small>
                                                </label>
                                                <select
                                                    onChange={(e) => {
                                                        if (e.target.value) {
                                                            const template = SELL_CONDITION_TEMPLATES.find(t => t.id === e.target.value);
                                                            if (template) {
                                                                // 检查是否已存在相同类型的条件
                                                                const existingCondition = formData.sellConditions.find(condition => condition.type === template.type);
                                                                if (existingCondition) {
                                                                    alert(`已存在"${template.name}"条件，每种类型只能添加一个。`);
                                                                    return;
                                                                }
                                                                
                                                                const newCondition = {
                                                                    id: `sell_${Date.now()}`,
                                                                    type: template.type,
                                                                    params: { ...template.defaultParams }
                                                                };
                                                                setFormData(prev => ({
                                                                    ...prev,
                                                                    sellConditions: [...prev.sellConditions, newCondition]
                                                                }));
                                                            }
                                                            e.target.value = '';
                                                        }
                                                    }}
                                                    style={{ width: '100%' }}
                                                >
                                                    <option value="">+ 添加卖出条件</option>
                                                    {SELL_CONDITION_TEMPLATES.map(template => {
                                                        // 检查该类型的条件是否已经存在
                                                        const isAlreadyAdded = formData.sellConditions.some(condition => condition.type === template.type);
                                                        return (
                                                            <option 
                                                                key={template.id} 
                                                                value={template.id}
                                                                disabled={isAlreadyAdded}
                                                                style={{ color: isAlreadyAdded ? '#999' : 'inherit' }}
                                                            >
                                                                {template.name}{isAlreadyAdded ? ' (已添加)' : ''}
                                                            </option>
                                                        );
                                                    })}
                                                </select>
                                            </div>

                                            {/* 已添加的卖出条件列表 */}
                                            {formData.sellConditions.length > 0 && (
                                                <div style={{ marginTop: '15px' }}>
                                                    {formData.sellConditions.map((condition, index) => {
                                                        const template = SELL_CONDITION_TEMPLATES.find(t => t.type === condition.type);
                                                        return (
                                                            <div key={condition.id} style={{ 
                                                                background: 'white', 
                                                                border: '1px solid #dee2e6', 
                                                                padding: '15px', 
                                                                marginBottom: '10px', 
                                                                borderRadius: '4px',
                                                                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                                                            }}>
                                                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                                                                    <span style={{ fontWeight: 'bold', color: '#495057' }}>{template?.name}</span>
                                                                    <button
                                                                        type="button"
                                                                        onClick={() => {
                                                                            setFormData(prev => ({
                                                                                ...prev,
                                                                                sellConditions: prev.sellConditions.filter(c => c.id !== condition.id)
                                                                            }));
                                                                        }}
                                                                        style={{ 
                                                                            background: '#dc3545', 
                                                                            color: 'white', 
                                                                            border: 'none', 
                                                                            padding: '4px 12px', 
                                                                            borderRadius: '3px', 
                                                                            cursor: 'pointer',
                                                                            fontSize: '12px'
                                                                        }}
                                                                    >
                                                                        删除
                                                                    </button>
                                                                </div>
                                                                <div style={{ fontSize: '13px', color: '#6c757d', marginBottom: '15px' }}>
                                                                    {template?.description}
                                                                </div>
                                                                
                                                                {/* 卖出条件参数配置 */}
                                                                {template && template.configFields.map(field => (
                                                                    <div key={field.name} className="form-group">
                                                                        {field.type !== 'multiselect' && (
                                                                            <label>
                                                                                {field.label}
                                                                                {field.required && <span className="required-asterisk"> *</span>}
                                                                            </label>
                                                                        )}
                                                                        
                                                                        {field.type === 'number' && (
                                                                            <input
                                                                                type="number"
                                                                                value={condition.params[field.name] || field.defaultValue || ''}
                                                                                onChange={(e) => {
                                                                                    const newValue = parseFloat(e.target.value) || 0;
                                                                                    setFormData(prev => ({
                                                                                        ...prev,
                                                                                        sellConditions: prev.sellConditions.map(c => 
                                                                                            c.id === condition.id 
                                                                                                ? { ...c, params: { ...c.params, [field.name]: newValue } }
                                                                                                : c
                                                                                        )
                                                                                    }));
                                                                                }}
                                                                                placeholder={field.placeholder}
                                                                                min={field.validation?.min}
                                                                                max={field.validation?.max}
                                                                                step={field.validation?.step || "any"}
                                                                            />
                                                                        )}
                                                                        
                                                                        {field.type === 'select' && (
                                                                            <select
                                                                                value={condition.params[field.name] || field.defaultValue || ''}
                                                                                onChange={(e) => {
                                                                                    setFormData(prev => ({
                                                                                        ...prev,
                                                                                        sellConditions: prev.sellConditions.map(c => 
                                                                                            c.id === condition.id 
                                                                                                ? { ...c, params: { ...c.params, [field.name]: e.target.value } }
                                                                                                : c
                                                                                        )
                                                                                    }));
                                                                                }}
                                                                            >
                                                                                {field.options?.map(option => (
                                                                                    <option key={option.value} value={option.value}>
                                                                                        {option.label}
                                                                                    </option>
                                                                                ))}
                                                                            </select>
                                                                        )}
                                                                        
                                                                        {field.type === 'multiselect' && (
                                                                            <div>
                                                                                {field.name === 'brokerFilter' && (
                                                                                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
                                                                                        <label style={{ margin: 0, fontWeight: 'bold' }}>
                                                                                            {field.label}
                                                                                            {field.required && <span className="required-asterisk"> *</span>}
                                                                                        </label>
                                                                                        <button
                                                                                            type="button"
                                                                                            onClick={() => setIsBrokerManagerOpen(true)}
                                                                                            style={{
                                                                                                background: '#6c757d',
                                                                                                color: 'white',
                                                                                                border: 'none',
                                                                                                padding: '4px 8px',
                                                                                                borderRadius: '3px',
                                                                                                cursor: 'pointer',
                                                                                                fontSize: '11px',
                                                                                                flexShrink: 0
                                                                                            }}
                                                                                        >
                                                                                            管理经纪商
                                                                                        </button>
                                                                                    </div>
                                                                                )}
                                                                                {field.name !== 'brokerFilter' && (
                                                                                    <label style={{ fontWeight: 'bold', marginBottom: '8px', display: 'block' }}>
                                                                                        {field.label}
                                                                                        {field.required && <span className="required-asterisk"> *</span>}
                                                                                    </label>
                                                                                )}
                                                                                <MultiSelectDropdown
                                                                                    options={field.name === 'brokerFilter' ? getCurrentBrokerOptions() : field.options || []}
                                                                                    value={condition.params[field.name] || field.defaultValue || []}
                                                                                    onChange={(selectedValues) => {
                                                                                        setFormData(prev => ({
                                                                                            ...prev,
                                                                                            sellConditions: prev.sellConditions.map(c => 
                                                                                                c.id === condition.id 
                                                                                                    ? { ...c, params: { ...c.params, [field.name]: selectedValues } }
                                                                                                    : c
                                                                                            )
                                                                                        }));
                                                                                    }}
                                                                                    placeholder={field.placeholder || '请选择...'}
                                                                                />
                                                                            </div>
                                                                        )}
                                                                        
                                                                        {field.helpText && field.name !== 'brokerFilter' && (
                                                                            <small className="field-help">{field.helpText}</small>
                                                                        )}
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        );
                                                    })}
                                                </div>
                                            )}

                                            {formData.sellConditions.length === 0 && (
                                                <div style={{ 
                                                    textAlign: 'center', 
                                                    color: '#6c757d', 
                                                    padding: '20px',
                                                    background: 'white',
                                                    border: '2px dashed #dee2e6',
                                                    borderRadius: '4px',
                                                    marginTop: '15px'
                                                }}>
                                                    请添加至少一个卖出触发条件
                                                </div>
                                            )}

                                            {/* 卖出执行方式 */}
                                            <div className="form-group" style={{ marginTop: '20px' }}>
                                                <label style={{ fontWeight: 'bold' }}>卖出执行方式<span className="required-asterisk"> *</span></label>
                                                <select
                                                    value={formData.sellExecutionType}
                                                    onChange={(e) => {
                                                        updateField('sellExecutionType', e.target.value);
                                                        const template = SELL_EXECUTION_TEMPLATES.find(t => t.id === e.target.value);
                                                        if (template) {
                                                            updateField('sellExecutionParams', { ...template.defaultParams });
                                                        }
                                                    }}
                                                    style={{ width: '100%' }}
                                                >
                                                    {SELL_EXECUTION_TEMPLATES.map(template => (
                                                        <option key={template.id} value={template.id}>
                                                            {template.name}
                                                        </option>
                                                    ))}
                                                </select>
                                                {selectedSellExecution && (
                                                    <small className="field-help">{selectedSellExecution.description}</small>
                                                )}
                                            </div>

                                            {/* 卖出执行参数配置 */}
                                            {selectedSellExecution && selectedSellExecution.configFields.map(field => (
                                                <div key={field.name} className="form-group">
                                                    <label>
                                                        {field.label}
                                                        {field.required && <span className="required-asterisk"> *</span>}
                                                    </label>
                                                    
                                                    {field.type === 'number' && (
                                                        <input
                                                            type="number"
                                                            value={formData.sellExecutionParams[field.name] || field.defaultValue || ''}
                                                            onChange={(e) => {
                                                                const newValue = parseFloat(e.target.value) || 0;
                                                                setFormData(prev => ({
                                                                    ...prev,
                                                                    sellExecutionParams: {
                                                                        ...prev.sellExecutionParams,
                                                                        [field.name]: newValue
                                                                    }
                                                                }));
                                                            }}
                                                            placeholder={field.placeholder}
                                                            min={field.validation?.min}
                                                            max={field.validation?.max}
                                                            step={field.validation?.step || "any"}
                                                        />
                                                    )}
                                                    
                                                    {field.type === 'select' && (
                                                        <select
                                                            value={formData.sellExecutionParams[field.name] || field.defaultValue || ''}
                                                            onChange={(e) => {
                                                                setFormData(prev => ({
                                                                    ...prev,
                                                                    sellExecutionParams: {
                                                                        ...prev.sellExecutionParams,
                                                                        [field.name]: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        >
                                                            {field.options?.map(option => (
                                                                <option key={option.value} value={option.value}>
                                                                    {option.label}
                                                                </option>
                                                            ))}
                                                        </select>
                                                    )}
                                                    
                                                    {field.helpText && (
                                                        <small className="field-help">{field.helpText}</small>
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div style={{ 
                                            textAlign: 'center', 
                                            color: '#6c757d', 
                                            padding: '30px',
                                            background: '#f8f9fa',
                                            border: '2px dashed #dee2e6',
                                            borderRadius: '6px'
                                        }}>
                                            <div style={{ fontSize: '48px', marginBottom: '10px' }}>💤</div>
                                            <div>卖出策略未启用</div>
                                            <div style={{ fontSize: '12px', marginTop: '5px' }}>点击右上角开关启用卖出策略配置</div>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    )}

                    {/* 应急风控配置标签页 */}
                    {activeTab === 'risk' && (
                        <div className="tab-content">
                            {/* 风控条件配置部分 */}
                            <div className="risk-conditions-section">
                                <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '15px' }}>
                                    <div style={{ flex: 1 }}>
                                        <h4 style={{ margin: 0, display: 'flex', alignItems: 'center', color: '#495057' }}>
                                            应急风控条件
                                        </h4>
                                        <p style={{ color: '#666', fontSize: '13px', margin: '5px 0 0 0', lineHeight: '1.4' }}>
                                            配置应急情况下的风控触发条件，当满足条件时系统将自动执行清仓操作
                                        </p>
                                    </div>
                                </div>

                                <div style={{ 
                                    background: '#f8f9fa', 
                                    border: '1px solid #e9ecef', 
                                    borderRadius: '6px', 
                                    padding: '20px' 
                                }}>
                                    <div className="form-group">
                                        <label style={{ fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
                                            触发逻辑
                                        </label>
                                        <div className="radio-group" style={{ display: 'flex', gap: '20px' }}>
                                            <label className="radio-label" style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', gap: '8px' }}>
                                                <input
                                                    type="radio"
                                                    name="triggerLogic"
                                                    value="any"
                                                    checked={formData.riskTriggerLogic === 'any'}
                                                    onChange={(e) => updateField('riskTriggerLogic', e.target.value)}
                                                    style={{ 
                                                        outline: 'none',
                                                        boxShadow: 'none',
                                                        margin: 'unset',
                                                        flexShrink: 0
                                                    }}
                                                />
                                                <span>满足任一条件 (OR)</span>
                                            </label>
                                            <label className="radio-label" style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', gap: '8px' }}>
                                                <input
                                                    type="radio"
                                                    name="triggerLogic"
                                                    value="all"
                                                    checked={formData.riskTriggerLogic === 'all'}
                                                    onChange={(e) => updateField('riskTriggerLogic', e.target.value)}
                                                    style={{ 
                                                        outline: 'none',
                                                        boxShadow: 'none',
                                                        margin: 'unset',
                                                        flexShrink: 0
                                                    }}
                                                />
                                                <span>满足全部条件 (AND)</span>
                                            </label>
                                        </div>
                                        <small className="field-help">选择多个条件时的触发逻辑</small>
                                    </div>

                                    <div className="form-group">
                                        <label style={{ fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
                                            应急条件列表
                                            <small style={{ fontWeight: 'normal', color: '#666', marginLeft: '8px' }}>
                                                (可添加多个条件)
                                            </small>
                                        </label>
                                        <select
                                            onChange={(e) => {
                                                if (e.target.value) {
                                                    addRiskCondition(e.target.value);
                                                    e.target.value = '';
                                                }
                                            }}
                                            style={{ width: '100%' }}
                                        >
                                            <option value="">+ 添加应急条件</option>
                                            {RISK_CONDITION_TEMPLATES.map(template => {
                                                // 检查该类型的条件是否已经存在
                                                const isAlreadyAdded = formData.riskConditions.some(condition => condition.type === template.type);
                                                return (
                                                    <option 
                                                        key={template.id} 
                                                        value={template.id}
                                                        disabled={isAlreadyAdded}
                                                        style={{ color: isAlreadyAdded ? '#999' : 'inherit' }}
                                                    >
                                                        {template.name}{isAlreadyAdded ? ' (已添加)' : ''}
                                                    </option>
                                                );
                                            })}
                                        </select>
                                    </div>

                                    {/* 已添加的风控条件列表 */}
                                    {formData.riskConditions.length > 0 && (
                                        <div style={{ marginTop: '15px' }}>
                                            {formData.riskConditions.map((condition, index) => {
                                                const template = RISK_CONDITION_TEMPLATES.find(t => t.type === condition.type);
                                                return (
                                                    <div key={condition.id} style={{ 
                                                        background: 'white', 
                                                        border: '1px solid #dee2e6', 
                                                        padding: '15px', 
                                                        marginBottom: '10px', 
                                                        borderRadius: '4px',
                                                        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                                                    }}>
                                                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                                                            <span style={{ fontWeight: 'bold', color: '#495057' }}>{template?.name}</span>
                                                            <button
                                                                type="button"
                                                                onClick={() => removeRiskCondition(condition.id)}
                                                                style={{ 
                                                                    background: '#dc3545', 
                                                                    color: 'white', 
                                                                    border: 'none', 
                                                                    padding: '4px 12px', 
                                                                    borderRadius: '3px', 
                                                                    cursor: 'pointer',
                                                                    fontSize: '12px'
                                                                }}
                                                            >
                                                                删除
                                                            </button>
                                                        </div>
                                                        <div style={{ fontSize: '13px', color: '#6c757d', marginBottom: '15px' }}>
                                                            {template?.description}
                                                        </div>
                                                        
                                                        {/* 条件配置字段 */}
                                                        {template && template.configFields.map(field => (
                                                            <div key={field.name} className="form-group">
                                                                <label>
                                                                    {field.label}
                                                                    {field.required && <span className="required-asterisk"> *</span>}
                                                                </label>
                                                                
                                                                {field.type === 'number' && (
                                                                    <input
                                                                        type="number"
                                                                        value={condition.params[field.name] || field.defaultValue || ''}
                                                                        onChange={(e) => updateRiskConditionParam(condition.id, field.name, parseFloat(e.target.value) || 0)}
                                                                        placeholder={field.placeholder}
                                                                        min={field.validation?.min}
                                                                        max={field.validation?.max}
                                                                        step={field.validation?.step || "any"}
                                                                    />
                                                                )}
                                                                
                                                                {field.type === 'text' && (
                                                                    <input
                                                                        type="text"
                                                                        value={condition.params[field.name] || field.defaultValue || ''}
                                                                        onChange={(e) => updateRiskConditionParam(condition.id, field.name, e.target.value)}
                                                                        placeholder={field.placeholder}
                                                                    />
                                                                )}
                                                                
                                                                {field.type === 'select' && (
                                                                    <select
                                                                        value={condition.params[field.name] || field.defaultValue || ''}
                                                                        onChange={(e) => updateRiskConditionParam(condition.id, field.name, e.target.value)}
                                                                    >
                                                                        {field.options?.map(option => (
                                                                            <option key={option.value} value={option.value}>
                                                                                {option.label}
                                                                            </option>
                                                                        ))}
                                                                    </select>
                                                                )}
                                                                
                                                                {field.helpText && (
                                                                    <small className="field-help">{field.helpText}</small>
                                                                )}
                                                            </div>
                                                        ))}
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    )}

                                    {formData.riskConditions.length === 0 && (
                                        <div style={{ 
                                            textAlign: 'center', 
                                            color: '#6c757d', 
                                            padding: '30px',
                                            background: 'white',
                                            border: '2px dashed #dee2e6',
                                            borderRadius: '6px',
                                            marginTop: '15px'
                                        }}>
                                            <div style={{ fontSize: '48px', marginBottom: '10px' }}>⚠️</div>
                                            <div style={{ fontWeight: '500' }}>暂未添加应急条件</div>
                                            <div style={{ fontSize: '12px', marginTop: '5px', color: '#868e96' }}>请添加至少一个风控条件以保护交易安全</div>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* 清仓执行策略部分 */}
                            <div className="liquidation-config-section" style={{ marginTop: '30px', borderTop: '1px solid #eee', paddingTop: '20px' }}>
                                <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '15px' }}>
                                    <div style={{ flex: 1 }}>
                                        <h4 style={{ margin: 0, display: 'flex', alignItems: 'center', color: '#495057' }}>
                                            清仓执行策略
                                            <span className="required-asterisk" style={{ marginLeft: '5px' }}> *</span>
                                        </h4>
                                        <p style={{ color: '#666', fontSize: '13px', margin: '5px 0 0 0', lineHeight: '1.4' }}>
                                            配置风控触发后的清仓执行方式，平衡执行速度和成本控制
                                        </p>
                                    </div>
                                </div>

                                <div style={{ 
                                    background: '#f8f9fa', 
                                    border: '1px solid #e9ecef', 
                                    borderRadius: '6px', 
                                    padding: '20px' 
                                }}>
                                    <div className="form-group">
                                        <label style={{ fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
                                            清仓执行策略
                                            <span className="required-asterisk"> *</span>
                                        </label>
                                        <select
                                            value={formData.liquidationStrategyId}
                                            onChange={(e) => handleLiquidationChange(e.target.value)}
                                            style={{ width: '100%' }}
                                        >
                                            {LIQUIDATION_TEMPLATES.map(template => (
                                                <option key={template.id} value={template.id}>
                                                    {template.name}
                                                </option>
                                            ))}
                                        </select>
                                        {selectedLiquidation && (
                                            <small className="field-help">{selectedLiquidation.description}</small>
                                        )}
                                    </div>

                                    {/* 清仓策略参数配置 */}
                                    {selectedLiquidation && selectedLiquidation.configFields.length > 0 && (
                                        <div style={{ marginTop: '20px' }}>
                                            <div style={{ 
                                                background: 'white', 
                                                border: '1px solid #dee2e6', 
                                                padding: '15px', 
                                                borderRadius: '4px',
                                                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                                            }}>
                                                <div style={{ marginBottom: '15px' }}>
                                                    <h5 style={{ margin: 0, fontWeight: 'bold', color: '#495057' }}>
                                                        {selectedLiquidation.name} 配置
                                                    </h5>
                                                </div>

                                                {selectedLiquidation.configFields.map(field => (
                                                    <div key={field.name} className="form-group">
                                                        <label>
                                                            {field.label}
                                                            {field.required && <span className="required-asterisk"> *</span>}
                                                        </label>
                                                        
                                                        {field.type === 'number' && (
                                                            <input
                                                                type="number"
                                                                value={formData.liquidationParams[field.name] || field.defaultValue || ''}
                                                                onChange={(e) => updateLiquidationParam(field.name, parseFloat(e.target.value) || 0)}
                                                                min={field.validation?.min}
                                                                max={field.validation?.max}
                                                                step={field.validation?.step || "any"}
                                                            />
                                                        )}
                                                        
                                                        {field.type === 'select' && (
                                                            <select
                                                                value={formData.liquidationParams[field.name] || field.defaultValue || ''}
                                                                onChange={(e) => updateLiquidationParam(field.name, e.target.value)}
                                                            >
                                                                {field.options?.map(option => (
                                                                    <option key={option.value} value={option.value}>
                                                                        {option.label}
                                                                    </option>
                                                                ))}
                                                            </select>
                                                        )}
                                                        
                                                        {field.helpText && (
                                                            <small className="field-help">{field.helpText}</small>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
                
                <div className="modal-footer">
                    <button className="btn btn-secondary" onClick={onClose}>
                        取消
                    </button>
                    <button className="btn btn-primary" onClick={handleSave}>
                        {task ? '保存修改' : '创建任务'}
                    </button>
                </div>
            </div>

            {/* 经纪商管理模态框 */}
            <BrokerManagerModal 
                isOpen={isBrokerManagerOpen}
                onClose={() => setIsBrokerManagerOpen(false)}
                onBrokerListUpdate={handleBrokerListUpdate}
            />
        </div>
    );
};