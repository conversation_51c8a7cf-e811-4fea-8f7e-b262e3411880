import React, { memo, useMemo, useCallback } from 'react';
import { Task } from '../../types';
import { TaskCardErrorBoundary } from './ErrorBoundary';
import { TaskHeader } from './TaskCard/TaskHeader';
import { TaskStatus } from './TaskCard/TaskStatus';
import { TaskMetrics } from './TaskCard/TaskMetrics';
import { TaskActions } from './TaskCard/TaskActions';
import { useTaskCardKeyboardNavigation } from './KeyboardInteractions';
import { ValueHighlight } from './AnimationSystem';

interface OptimizedTaskCardProps {
    task: Task;
    onToggle: (taskId: string) => void;
    onShowDetails: (taskId: string) => void;
    onEdit: (taskId: string) => void;
    onDelete: (taskId: string) => void;
    onLiquidate: (taskId: string) => void;
}

// 性能优化的比较函数
const arePropsEqual = (prevProps: OptimizedTaskCardProps, nextProps: OptimizedTaskCardProps) => {
    const prevTask = prevProps.task;
    const nextTask = nextProps.task;
    
    // 只在关键数据变化时重新渲染
    return (
        prevTask.id === nextTask.id &&
        prevTask.status === nextTask.status &&
        prevTask.pnl === nextTask.pnl &&
        prevTask.position === nextTask.position &&
        prevTask.updatedAt === nextTask.updatedAt &&
        // 检查处理函数引用是否稳定
        prevProps.onToggle === nextProps.onToggle &&
        prevProps.onShowDetails === nextProps.onShowDetails &&
        prevProps.onEdit === nextProps.onEdit &&
        prevProps.onDelete === nextProps.onDelete &&
        prevProps.onLiquidate === nextProps.onLiquidate
    );
};

// 优化后的TaskCard组件
export const OptimizedTaskCard = memo<OptimizedTaskCardProps>(({ 
    task, 
    onToggle,
    onShowDetails,
    onEdit,
    onDelete,
    onLiquidate
}) => {
    // 使用useMemo缓存计算结果
    const pnlPercentage = useMemo(() => {
        if (!task.avgCost || task.position === 0) return 0;
        return (task.pnl / (task.avgCost * task.position)) * 100;
    }, [task.pnl, task.avgCost, task.position]);

    const currentPrice = useMemo(() => {
        if (!task.avgCost || task.position === 0) return null;
        return task.avgCost + (task.pnl / task.position);
    }, [task.avgCost, task.pnl, task.position]);

    const runningDuration = useMemo(() => {
        const now = new Date();
        const created = new Date(task.createdAt);
        const hours = Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60));
        
        if (hours < 24) {
            return `${hours}小时`;
        } else {
            const days = Math.floor(hours / 24);
            return `${days}天`;
        }
    }, [task.createdAt]);

    // 使用useCallback稳定事件处理函数
    const handleToggle = useCallback(() => onToggle(task.id), [task.id, onToggle]);
    const handleShowDetails = useCallback(() => onShowDetails(task.id), [task.id, onShowDetails]);
    const handleEdit = useCallback(() => onEdit(task.id), [task.id, onEdit]);
    const handleDelete = useCallback(() => onDelete(task.id), [task.id, onDelete]);
    const handleLiquidate = useCallback(() => onLiquidate(task.id), [task.id, onLiquidate]);

    // 键盘导航
    const { handleKeyDown } = useTaskCardKeyboardNavigation(task.id, {
        onToggle: handleToggle,
        onShowDetails: handleShowDetails,
        onEdit: handleEdit,
        onDelete: handleDelete,
        onLiquidate: handleLiquidate
    });

    // 获取卡片样式类
    const getCardClassName = useMemo(() => {
        const baseClasses = 'task-card';
        const statusClasses = `status-${task.status}`;
        return `${baseClasses} ${statusClasses}`;
    }, [task.status]);

    return (
        <TaskCardErrorBoundary taskId={task.id}>
            <div
                className={getCardClassName}
                tabIndex={0}
                role="article"
                aria-label={`${task.stockName} ${task.strategyName} 任务卡片`}
                onKeyDown={handleKeyDown}
                style={{
                    background: '#f8f9fa',
                    border: '1px solid #e9ecef',
                    borderRadius: '8px',
                    padding: '16px',
                    position: 'relative',
                    borderLeft: `4px solid ${getStatusBorderColor(task.status)}`,
                    transition: 'all 0.2s ease',
                    cursor: 'pointer'
                }}
            >
                {/* 任务头部信息 */}
                <TaskHeader
                    stockCode={task.stockCode}
                    stockName={task.stockName}
                    strategyName={task.strategyName}
                />

                {/* 状态指示器 */}
                <TaskStatus
                    status={task.status}
                    runningDuration={runningDuration}
                />

                {/* 关键指标展示 - 带数值变化高亮 */}
                <ValueHighlight value={task.pnl}>
                    <TaskMetrics
                        pnl={task.pnl}
                        pnlPercentage={pnlPercentage}
                        position={task.position}
                        avgCost={task.avgCost}
                        currentPrice={currentPrice}
                    />
                </ValueHighlight>

                {/* 操作按钮组 */}
                <TaskActions
                    task={task}
                    onToggle={handleToggle}
                    onShowDetails={handleShowDetails}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    onLiquidate={handleLiquidate}
                />
            </div>
        </TaskCardErrorBoundary>
    );
}, arePropsEqual);

// 获取状态边框颜色
function getStatusBorderColor(status: string): string {
    const colors = {
        running: '#28a745',
        paused: '#ffc107',
        stopped: '#6c757d',
        error: '#dc3545',
        liquidated: '#007bff'
    };
    return colors[status] || '#6c757d';
}

// 虚拟化TaskCard列表组件
import { FixedSizeGrid as Grid, GridChildComponentProps } from 'react-window';

interface VirtualizedTaskListProps {
    tasks: Task[];
    onToggleTask: (taskId: string) => void;
    onShowDetails: (taskId: string) => void;
    onEditTask: (taskId: string) => void;
    onDeleteTask: (taskId: string) => void;
    onLiquidateTask: (taskId: string) => void;
    columns: number;
    cardHeight?: number;
    cardWidth?: number;
    containerHeight?: number;
}

export const VirtualizedTaskList: React.FC<VirtualizedTaskListProps> = memo(({
    tasks,
    onToggleTask,
    onShowDetails,
    onEditTask,
    onDeleteTask,
    onLiquidateTask,
    columns,
    cardHeight = 320,
    cardWidth = 320,
    containerHeight = 600
}) => {
    // 计算行数
    const rowCount = Math.ceil(tasks.length / columns);

    // 渲染单个任务卡片
    const Cell = memo(({ columnIndex, rowIndex, style }: GridChildComponentProps) => {
        const taskIndex = rowIndex * columns + columnIndex;
        const task = tasks[taskIndex];

        if (!task) return null;

        return (
            <div style={{ ...style, padding: '10px' }}>
                <OptimizedTaskCard
                    task={task}
                    onToggle={onToggleTask}
                    onShowDetails={onShowDetails}
                    onEdit={onEditTask}
                    onDelete={onDeleteTask}
                    onLiquidate={onLiquidateTask}
                />
            </div>
        );
    });

    return (
        <Grid
            columnCount={columns}
            columnWidth={cardWidth}
            height={containerHeight}
            rowCount={rowCount}
            rowHeight={cardHeight}
            width={columns * cardWidth + (columns - 1) * 20} // 包含间距
        >
            {Cell}
        </Grid>
    );
});