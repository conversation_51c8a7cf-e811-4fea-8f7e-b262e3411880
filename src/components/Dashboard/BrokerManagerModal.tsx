import React, { useState, useEffect } from 'react';
import { Broker } from '../../types';
import { brokerManager } from '../../services/brokerManager';

interface BrokerManagerModalProps {
    isOpen: boolean;
    onClose: () => void;
    onBrokerListUpdate: () => void;
}

export const BrokerManagerModal: React.FC<BrokerManagerModalProps> = ({
    isOpen,
    onClose,
    onBrokerListUpdate
}) => {
    const [brokers, setBrokers] = useState<Broker[]>([]);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [editingBroker, setEditingBroker] = useState<Broker | null>(null);
    const [formData, setFormData] = useState({ name: '', code: '' });
    const [error, setError] = useState('');

    // 加载经纪商列表
    const loadBrokers = () => {
        setBrokers(brokerManager.getAllBrokers());
    };

    useEffect(() => {
        if (isOpen) {
            loadBrokers();
        }
    }, [isOpen]);

    // 重置表单
    const resetForm = () => {
        setFormData({ name: '', code: '' });
        setError('');
        setIsEditModalOpen(false);
        setEditingBroker(null);
    };

    // 处理添加经纪商
    const handleAdd = () => {
        const result = brokerManager.addBroker(formData.name, formData.code);
        if (result.success) {
            loadBrokers();
            resetForm();
            onBrokerListUpdate();
        } else {
            setError(result.message);
        }
    };

    // 处理更新经纪商
    const handleUpdate = () => {
        if (!editingBroker) return;
        
        const result = brokerManager.updateBroker(editingBroker.id, formData.name, formData.code);
        if (result.success) {
            loadBrokers();
            resetForm();
            onBrokerListUpdate();
        } else {
            setError(result.message);
        }
    };

    // 处理删除经纪商
    const handleDelete = (id: string) => {
        if (confirm('确定要删除这个经纪商吗？')) {
            const result = brokerManager.deleteBroker(id);
            if (result.success) {
                loadBrokers();
                onBrokerListUpdate();
            } else {
                alert(result.message);
            }
        }
    };

    // 开始编辑
    const startEdit = (broker: Broker) => {
        setEditingBroker(broker);
        setFormData({ name: broker.name, code: broker.code });
        setError('');
        setIsEditModalOpen(true);
    };

    // 开始添加
    const startAdd = () => {
        setEditingBroker(null);
        setFormData({ name: '', code: '' });
        setError('');
        setIsEditModalOpen(true);
    };

    // 处理提交
    const handleSubmit = () => {
        if (editingBroker) {
            handleUpdate();
        } else {
            handleAdd();
        }
    };

    if (!isOpen) return null;

    return (
        <>
            <div 
                className="modal-overlay" 
                onClick={(e) => {
                    // 如果有编辑弹框打开，不关闭主弹框
                    if (isEditModalOpen) {
                        return;
                    }
                    // 只有点击遮罩层本身才关闭弹框
                    if (e.target === e.currentTarget) {
                        onClose();
                    }
                }}
            >
                <div 
                    className="modal-content" 
                    onClick={e => e.stopPropagation()} 
                    style={{ 
                        maxWidth: '600px',
                        height: '80vh',
                        display: 'flex',
                        flexDirection: 'column'
                    }}
                >
                    <div className="modal-header" style={{ flexShrink: 0 }}>
                        <h2 style={{ color: '#212529' }}>经纪商管理</h2>
                        <button className="modal-close" onClick={onClose}>×</button>
                    </div>

                    {/* 固定的标题和操作栏 */}
                    <div style={{ 
                        borderBottom: '1px solid #e9ecef',
                        padding: '20px 20px 15px 20px',
                        flexShrink: 0
                    }}>
                        <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                            <div style={{ flex: 1 }}>
                                <h4 style={{ margin: 0, display: 'flex', alignItems: 'center', color: '#495057' }}>
                                    经纪商列表
                                </h4>
                                <p style={{ color: '#666', fontSize: '13px', margin: '5px 0 0 0', lineHeight: '1.4' }}>
                                    管理交易策略中使用的经纪商，可以添加自定义经纪商或删除不需要的经纪商
                                </p>
                            </div>
                            <div style={{ flexShrink: 0 }}>
                                <button
                                    onClick={startAdd}
                                    style={{
                                        background: '#007bff',
                                        color: 'white',
                                        border: 'none',
                                        padding: '6px 12px',
                                        borderRadius: '4px',
                                        cursor: 'pointer',
                                        fontSize: '12px'
                                    }}
                                >
                                    + 添加经纪商
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* 可滚动的经纪商列表内容 */}
                    <div style={{ 
                        flex: 1,
                        padding: '20px',
                        overflowY: 'auto',
                        minHeight: 0
                    }}>
                        <div style={{ 
                            background: '#f8f9fa', 
                            border: '1px solid #e9ecef', 
                            borderRadius: '6px', 
                            padding: '20px' 
                        }}>
                            {/* 经纪商列表 */}
                            {brokers.length > 0 ? (
                                <div>
                                    {brokers.map((broker) => (
                                        <div key={broker.id} style={{ 
                                            background: 'white', 
                                            border: '1px solid #dee2e6', 
                                            padding: '15px', 
                                            marginBottom: '10px', 
                                            borderRadius: '4px',
                                            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                                        }}>
                                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                                <div style={{ flex: 1 }}>
                                                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '5px' }}>
                                                        <span style={{ fontWeight: 'bold', color: '#495057' }}>
                                                            {broker.name}
                                                        </span>
                                                    </div>
                                                    <div style={{ fontSize: '12px', color: '#6c757d' }}>
                                                        代码: {broker.code}
                                                    </div>
                                                </div>
                                                <div style={{ display: 'flex', gap: '8px' }}>
                                                    <button
                                                        onClick={() => startEdit(broker)}
                                                        style={{
                                                            background: '#6c757d',
                                                            color: 'white',
                                                            border: 'none',
                                                            padding: '4px 8px',
                                                            borderRadius: '3px',
                                                            cursor: 'pointer',
                                                            fontSize: '11px'
                                                        }}
                                                    >
                                                        编辑
                                                    </button>
                                                    <button
                                                        onClick={() => handleDelete(broker.id)}
                                                        style={{
                                                            background: '#dc3545',
                                                            color: 'white',
                                                            border: 'none',
                                                            padding: '4px 8px',
                                                            borderRadius: '3px',
                                                            cursor: 'pointer',
                                                            fontSize: '11px'
                                                        }}
                                                    >
                                                        删除
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div style={{ 
                                    textAlign: 'center', 
                                    color: '#6c757d', 
                                    padding: '30px',
                                    background: 'white',
                                    border: '2px dashed #dee2e6',
                                    borderRadius: '6px'
                                }}>
                                    <div style={{ fontSize: '48px', marginBottom: '10px' }}>🏦</div>
                                    <div style={{ fontWeight: '500' }}>暂无经纪商</div>
                                    <div style={{ fontSize: '12px', marginTop: '5px', color: '#868e96' }}>点击上方按钮添加经纪商</div>
                                </div>
                            )}
                        </div>
                    </div>
                    
                    {/* 固定的底部操作栏 */}
                    <div className="modal-footer" style={{ flexShrink: 0 }}>
                        <button 
                            className="btn btn-secondary" 
                            onClick={onClose}
                        >
                            取消
                        </button>
                        <button className="btn btn-primary" onClick={onClose}>
                            完成
                        </button>
                    </div>
                </div>
            </div>

            {/* 经纪商管理弹框 */}
            {isEditModalOpen && (
                <div 
                    className="modal-overlay" 
                    onClick={(e) => {
                        // 只有点击遮罩层本身才关闭弹框，阻止事件传播
                        if (e.target === e.currentTarget) {
                            e.stopPropagation(); // 阻止事件传播到底层弹框
                            resetForm();
                        }
                    }} 
                    style={{ zIndex: 1001 }}
                >
                    <div className="modal-content" onClick={e => e.stopPropagation()} style={{ maxWidth: '400px' }}>
                        <div className="modal-header">
                            <h3 style={{ color: '#212529' }}>{editingBroker ? '编辑经纪商' : '添加新经纪商'}</h3>
                            <button className="modal-close" onClick={resetForm}>×</button>
                        </div>

                        <div className="modal-body" style={{ padding: '20px' }}>
                            {error && (
                                <div style={{ 
                                    background: '#f8d7da', 
                                    border: '1px solid #f5c6cb', 
                                    color: '#721c24', 
                                    padding: '10px', 
                                    borderRadius: '4px', 
                                    marginBottom: '15px',
                                    fontSize: '14px'
                                }}>
                                    {error}
                                </div>
                            )}

                            <div className="form-group" style={{ marginBottom: '20px' }}>
                                <label style={{ fontWeight: 'bold', marginBottom: '8px', display: 'block', color: '#495057' }}>
                                    经纪商名称
                                    <span className="required-asterisk" style={{ color: '#dc3545' }}> *</span>
                                </label>
                                <input
                                    type="text"
                                    value={formData.name}
                                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                                    placeholder="如：招商证券"
                                    style={{ 
                                        width: '100%',
                                        padding: '8px 12px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                                <small className="field-help" style={{ 
                                    color: '#6c757d', 
                                    fontSize: '12px', 
                                    marginTop: '4px', 
                                    display: 'block' 
                                }}>
                                    显示在选择列表中的经纪商名称
                                </small>
                            </div>

                            <div className="form-group" style={{ marginBottom: '0' }}>
                                <label style={{ fontWeight: 'bold', marginBottom: '8px', display: 'block', color: '#495057' }}>
                                    经纪代码
                                </label>
                                <input
                                    type="text"
                                    value={formData.code}
                                    onChange={(e) => setFormData(prev => ({ ...prev, code: e.target.value }))}
                                    placeholder="如：cmb_securities（可选）"
                                    style={{ 
                                        width: '100%',
                                        padding: '8px 12px',
                                        border: '1px solid #ced4da',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                    }}
                                />
                                <small className="field-help" style={{ 
                                    color: '#6c757d', 
                                    fontSize: '12px', 
                                    marginTop: '4px', 
                                    display: 'block' 
                                }}>
                                    用于系统识别的代码，可选填写，建议使用英文和下划线
                                </small>
                            </div>
                        </div>

                        <div className="modal-footer" style={{ padding: '15px 20px', borderTop: '1px solid #e9ecef' }}>
                            <button
                                className="btn btn-secondary"
                                onClick={resetForm}
                                style={{ marginRight: '10px' }}
                            >
                                取消
                            </button>
                            <button
                                className="btn btn-primary"
                                onClick={handleSubmit}
                                disabled={!formData.name.trim()}
                                style={{
                                    opacity: (!formData.name.trim()) ? 0.6 : 1,
                                    cursor: (!formData.name.trim()) ? 'not-allowed' : 'pointer'
                                }}
                            >
                                {editingBroker ? '更新' : '添加'}
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};