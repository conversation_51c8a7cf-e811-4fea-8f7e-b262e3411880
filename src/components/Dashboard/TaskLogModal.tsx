import React, { useState, useEffect } from 'react';
import { Task, LogEntry, LogLevel, LogCategory } from '../../types';
import { LogSystem } from './LogSystem';
import { AnimatedComponent } from './AnimationSystem';

interface TaskLogModalProps {
    task: Task;
    logs: LogEntry[];
    onClose: () => void;
}

type LogFilter = 'all' | LogLevel;

export const TaskLogModal: React.FC<TaskLogModalProps> = ({
    task,
    logs,
    onClose
}) => {
    const [selectedFilter, setSelectedFilter] = useState<LogFilter>('all');
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 50;

    // 过滤日志
    const filteredLogs = selectedFilter === 'all' 
        ? logs 
        : logs.filter(log => log.level === selectedFilter);

    // 分页处理
    const totalPages = Math.ceil(filteredLogs.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const displayLogs = filteredLogs.slice(startIndex, startIndex + itemsPerPage);

    // 键盘事件处理
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                onClose();
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [onClose]);

    const getFilterLabel = (filter: LogFilter) => {
        switch (filter) {
            case 'all': return '全部';
            case 'error': return '错误';
            case 'warning': return '警告';
            case 'info': return '信息';
            case 'debug': return '调试';
            default: return filter;
        }
    };

    const getFilterIcon = (filter: LogFilter) => {
        switch (filter) {
            case 'all': return '📋';
            case 'error': return '❌';
            case 'warning': return '⚠️';
            case 'info': return 'ℹ️';
            case 'debug': return '🔍';
            default: return '📝';
        }
    };

    const formatTimestamp = (date: Date) => {
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }).format(date);
    };

    const getLevelStyle = (level: LogLevel) => {
        const styles = {
            error: { color: '#dc3545', backgroundColor: '#f8d7da' },
            warning: { color: '#856404', backgroundColor: '#fff3cd' },
            info: { color: '#0c5460', backgroundColor: '#d1ecf1' },
            debug: { color: '#495057', backgroundColor: '#f8f9fa' }
        };
        return styles[level] || styles.info;
    };

    return (
        <div
            style={{
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 1000,
                padding: '20px'
            }}
            onClick={(e) => {
                if (e.target === e.currentTarget) onClose();
            }}
        >
            <AnimatedComponent animation="scaleIn" trigger={true}>
                <div
                    style={{
                        background: 'white',
                        borderRadius: '8px',
                        maxWidth: '900px',
                        width: '100%',
                        maxHeight: '80vh',
                        display: 'flex',
                        flexDirection: 'column',
                        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
                        overflow: 'hidden'
                    }}
                >
                    {/* Header */}
                    <div
                        style={{
                            background: '#f8f9fa',
                            border: '1px solid #e9ecef',
                            borderRadius: '8px 8px 0 0',
                            padding: '16px 20px',
                            borderBottom: '1px solid #e9ecef',
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center'
                        }}
                    >
                        <div>
                            <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600', color: '#495057' }}>
                                📊 任务日志 - {task.name}
                            </h3>
                            <p style={{ margin: '4px 0 0 0', fontSize: '13px', color: '#6c757d' }}>
                                {task.stockCode} • {task.strategyName}
                            </p>
                        </div>
                        <button
                            onClick={onClose}
                            style={{
                                background: 'none',
                                border: 'none',
                                fontSize: '20px',
                                cursor: 'pointer',
                                color: '#6c757d',
                                padding: '4px',
                                borderRadius: '4px',
                                transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = '#e9ecef';
                                e.currentTarget.style.color = '#495057';
                            }}
                            onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'transparent';
                                e.currentTarget.style.color = '#6c757d';
                            }}
                        >
                            ✕
                        </button>
                    </div>

                    {/* 过滤器 */}
                    <div
                        style={{
                            background: 'white',
                            padding: '16px 20px',
                            borderBottom: '1px solid #e9ecef',
                            display: 'flex',
                            gap: '8px',
                            alignItems: 'center',
                            flexWrap: 'wrap'
                        }}
                    >
                        <span style={{ fontSize: '14px', fontWeight: '500', color: '#495057', marginRight: '8px' }}>
                            日志级别:
                        </span>
                        {(['all', 'error', 'warning', 'info', 'debug'] as LogFilter[]).map((filter) => (
                            <button
                                key={filter}
                                onClick={() => {
                                    setSelectedFilter(filter);
                                    setCurrentPage(1);
                                }}
                                style={{
                                    padding: '6px 12px',
                                    fontSize: '12px',
                                    border: '1px solid #dee2e6',
                                    borderRadius: '4px',
                                    cursor: 'pointer',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '4px',
                                    transition: 'all 0.2s ease',
                                    backgroundColor: selectedFilter === filter ? '#007bff' : 'white',
                                    color: selectedFilter === filter ? 'white' : '#495057'
                                }}
                                onMouseEnter={(e) => {
                                    if (selectedFilter !== filter) {
                                        e.currentTarget.style.backgroundColor = '#f8f9fa';
                                    }
                                }}
                                onMouseLeave={(e) => {
                                    if (selectedFilter !== filter) {
                                        e.currentTarget.style.backgroundColor = 'white';
                                    }
                                }}
                            >
                                <span>{getFilterIcon(filter)}</span>
                                {getFilterLabel(filter)}
                                <span style={{ 
                                    fontSize: '11px', 
                                    opacity: 0.8,
                                    marginLeft: '4px',
                                    backgroundColor: selectedFilter === filter ? 'rgba(255,255,255,0.2)' : '#e9ecef',
                                    padding: '1px 6px',
                                    borderRadius: '10px'
                                }}>
                                    {filter === 'all' ? logs.length : logs.filter(l => l.level === filter).length}
                                </span>
                            </button>
                        ))}
                    </div>

                    {/* 日志内容 */}
                    <div
                        style={{
                            flex: 1,
                            overflow: 'auto',
                            padding: '16px 20px'
                        }}
                    >
                        {displayLogs.length === 0 ? (
                            <div
                                style={{
                                    textAlign: 'center',
                                    color: '#6c757d',
                                    padding: '40px',
                                    background: '#f8f9fa',
                                    border: '2px dashed #dee2e6',
                                    borderRadius: '6px'
                                }}
                            >
                                <div style={{ fontSize: '48px', marginBottom: '16px' }}>📝</div>
                                <div style={{ fontWeight: '500', fontSize: '16px', marginBottom: '8px' }}>
                                    暂无{selectedFilter === 'all' ? '' : getFilterLabel(selectedFilter)}日志
                                </div>
                                <div style={{ fontSize: '14px', color: '#868e96' }}>
                                    {selectedFilter === 'all' 
                                        ? '该任务还没有生成任何日志记录' 
                                        : `该任务没有${getFilterLabel(selectedFilter)}级别的日志记录`
                                    }
                                </div>
                            </div>
                        ) : (
                            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                {displayLogs.map((log) => (
                                    <div
                                        key={log.id}
                                        style={{
                                            border: '1px solid #e9ecef',
                                            borderRadius: '6px',
                                            padding: '12px',
                                            backgroundColor: 'white',
                                            borderLeft: `4px solid ${getLevelStyle(log.level).color}`
                                        }}
                                    >
                                        <div
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: '8px',
                                                marginBottom: '6px',
                                                fontSize: '12px'
                                            }}
                                        >
                                            <span
                                                style={{
                                                    padding: '2px 8px',
                                                    borderRadius: '12px',
                                                    fontSize: '11px',
                                                    fontWeight: '500',
                                                    ...getLevelStyle(log.level)
                                                }}
                                            >
                                                {getFilterIcon(log.level as LogFilter)} {getFilterLabel(log.level as LogFilter)}
                                            </span>
                                            <span style={{ color: '#6c757d' }}>
                                                {formatTimestamp(log.timestamp)}
                                            </span>
                                            {log.category && (
                                                <span
                                                    style={{
                                                        backgroundColor: '#e9ecef',
                                                        color: '#495057',
                                                        padding: '2px 6px',
                                                        borderRadius: '8px',
                                                        fontSize: '10px'
                                                    }}
                                                >
                                                    {log.category === 'strategy' ? '策略' : 
                                                     log.category === 'trading' ? '交易' : 
                                                     log.category === 'system' ? '系统' : log.category}
                                                </span>
                                            )}
                                        </div>
                                        <div style={{ fontSize: '14px', color: '#495057', lineHeight: '1.4' }}>
                                            {log.message}
                                        </div>
                                        {log.details && (
                                            <div
                                                style={{
                                                    marginTop: '8px',
                                                    padding: '8px',
                                                    backgroundColor: '#f8f9fa',
                                                    borderRadius: '4px',
                                                    fontSize: '12px',
                                                    fontFamily: 'monospace',
                                                    color: '#495057'
                                                }}
                                            >
                                                {JSON.stringify(log.details, null, 2)}
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* 分页 */}
                    {totalPages > 1 && (
                        <div
                            style={{
                                padding: '16px 20px',
                                borderTop: '1px solid #e9ecef',
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                background: '#f8f9fa'
                            }}
                        >
                            <div style={{ fontSize: '14px', color: '#6c757d' }}>
                                显示 {startIndex + 1}-{Math.min(startIndex + itemsPerPage, filteredLogs.length)} 条，
                                共 {filteredLogs.length} 条记录
                            </div>
                            <div style={{ display: 'flex', gap: '8px' }}>
                                <button
                                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                    disabled={currentPage === 1}
                                    style={{
                                        padding: '6px 12px',
                                        fontSize: '12px',
                                        border: '1px solid #dee2e6',
                                        borderRadius: '4px',
                                        cursor: currentPage === 1 ? 'not-allowed' : 'pointer',
                                        backgroundColor: currentPage === 1 ? '#f8f9fa' : 'white',
                                        color: currentPage === 1 ? '#6c757d' : '#495057'
                                    }}
                                >
                                    上一页
                                </button>
                                <span style={{ 
                                    padding: '6px 12px', 
                                    fontSize: '12px', 
                                    color: '#495057',
                                    display: 'flex',
                                    alignItems: 'center'
                                }}>
                                    {currentPage} / {totalPages}
                                </span>
                                <button
                                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                    disabled={currentPage === totalPages}
                                    style={{
                                        padding: '6px 12px',
                                        fontSize: '12px',
                                        border: '1px solid #dee2e6',
                                        borderRadius: '4px',
                                        cursor: currentPage === totalPages ? 'not-allowed' : 'pointer',
                                        backgroundColor: currentPage === totalPages ? '#f8f9fa' : 'white',
                                        color: currentPage === totalPages ? '#6c757d' : '#495057'
                                    }}
                                >
                                    下一页
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </AnimatedComponent>
        </div>
    );
};