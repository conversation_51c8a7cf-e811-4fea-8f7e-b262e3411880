/**
 * useElectronDashboard - Electron 版本的 Dashboard Hook
 * 替代原有的 Tauri 版本，与 Electron 交易系统进行真实通信
 */

import { useState, useEffect, useCallback } from "react";
import { electronTradingClient } from "../communication";
// 导入主要类型定义（来自 index.ts）
import type { Task, TaskStatus, LogEntry as MainLogEntry } from "../types";

// 导入 Electron 特定的类型
import type { TaskCreateOptions, TradingSystemStatus, Market, StrategyType, LiquidationType } from "../types/electron";

// 保持与原版本兼容的类型定义
interface GlobalStatus {
    totalTasks: number;
    runningTasks: number;
    totalMarketValue: number;
    totalPnL: number;
    connections: {
        market: "connected" | "disconnected" | "connecting" | "error";
        trading: "connected" | "disconnected" | "connecting" | "error";
    };
    tradingSystemStatus?: TradingSystemStatus;
}

interface LogEntry {
    id: string;
    timestamp: Date;
    level: "info" | "warning" | "error" | "debug";
    category: "strategy" | "trading" | "system";
    message: string;
    taskId: string;
    taskName: string;
    details?: any;
}

interface UseElectronDashboardReturn {
    // 状态
    tasks: Task[];
    globalStatus: GlobalStatus;
    isLoading: boolean;
    selectedTask: Task | null;
    showConfigModal: boolean;
    showDetailsModal: boolean;

    // 操作函数
    startAllTasks: () => Promise<void>;
    stopAllTasks: () => Promise<void>;
    addTask: (taskConfig: Partial<Task>) => Promise<void>;
    updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>;
    deleteTask: (taskId: string) => Promise<void>;
    toggleTask: (taskId: string) => Promise<void>;
    liquidateTask: (taskId: string) => Promise<void>;
    copyTask: (taskId: string) => Promise<void>;
    getTaskLogs: (taskId: string) => Promise<LogEntry[]>;

    // Modal 控制
    openConfigModal: (task?: Task) => void;
    closeConfigModal: () => void;
    openDetailsModal: (taskId: string) => void;
    closeDetailsModal: () => void;

    // 配置相关
    saveTaskConfig: (taskConfig: Partial<Task>) => Promise<void>;
    editTask: (taskId: string) => void;
}

export const useElectronDashboard = (): UseElectronDashboardReturn => {
    // 核心状态
    const [tasks, setTasks] = useState<Task[]>([]);
    const [globalStatus, setGlobalStatus] = useState<GlobalStatus>({
        totalTasks: 0,
        runningTasks: 0,
        totalMarketValue: 0,
        totalPnL: 0,
        connections: {
            market: "disconnected",
            trading: "disconnected"
        }
    });
    const [isLoading, setIsLoading] = useState(false);

    // Modal 状态
    const [selectedTask, setSelectedTask] = useState<Task | null>(null);
    const [showConfigModal, setShowConfigModal] = useState(false);
    const [showDetailsModal, setShowDetailsModal] = useState(false);

    // 获取任务列表
    const fetchTasks = useCallback(async () => {
        setIsLoading(true);
        try {
            const response = await electronTradingClient.getTaskList();
            if (response.success && response.data) {
                setTasks(response.data);
                updateGlobalStatus(response.data);
            } else {
                console.error("获取任务列表失败:", response.message);
                // 设置空列表避免界面错误
                setTasks([]);
            }
        } catch (error) {
            console.error("获取任务列表异常:", error);
            setTasks([]);
        } finally {
            setIsLoading(false);
        }
    }, []);

    // 获取交易系统状态
    const fetchSystemStatus = useCallback(async () => {
        try {
            const response = await electronTradingClient.getTradingSystemStatus();
            if (response.success && response.data) {
                const systemStatus = response.data as TradingSystemStatus;

                // 优先使用ServiceManager的详细状态
                let marketConnected = false;
                let tradingConnected = false;

                if (systemStatus.services) {
                    // 从ServiceManager获取精确状态
                    const marketService = systemStatus.services.marketData;
                    const tradingService = systemStatus.services.trading;

                    // 只有当服务启用且连接成功时才显示为connected
                    marketConnected = marketService ? marketService.enabled && marketService.connected && marketService.status === "running" : false;
                    tradingConnected = tradingService ? tradingService.enabled && tradingService.connected && tradingService.status === "running" : false;
                } else {
                    // 回退到传统状态
                    marketConnected = systemStatus.marketConnected;
                    tradingConnected = systemStatus.tradingConnected;
                }

                setGlobalStatus((prev) => ({
                    ...prev,
                    tradingSystemStatus: systemStatus,
                    connections: {
                        market: marketConnected ? "connected" : "disconnected",
                        trading: tradingConnected ? "connected" : "disconnected"
                    }
                }));
            }
        } catch (error) {
            console.error("获取系统状态失败:", error);
        }
    }, []);

    // 更新全局状态
    const updateGlobalStatus = (taskList: Task[]) => {
        const runningTasks = taskList.filter((task) => task.status === "running").length;
        const totalPnL = taskList.reduce((sum, task) => sum + task.pnl, 0);
        const totalMarketValue = taskList.reduce((sum, task) => {
            if (task.position > 0 && task.avgCost) {
                return sum + (task.position * task.avgCost + task.pnl);
            }
            return sum;
        }, 0);

        setGlobalStatus((prev) => ({
            ...prev,
            totalTasks: taskList.length,
            runningTasks,
            totalMarketValue,
            totalPnL
        }));
    };

    // 任务操作函数
    const startAllTasks = async () => {
        setIsLoading(true);
        try {
            // 启动所有暂停或停止的任务
            const tasksToStart = tasks.filter((task) => task.status === "paused" || task.status === "stopped");

            for (const task of tasksToStart) {
                await electronTradingClient.startTask(task.id);
            }

            // 重新获取任务列表
            await fetchTasks();
            console.log("已启动所有任务");
        } catch (error) {
            console.error("启动所有任务失败:", error);
            alert("启动任务失败: " + (error instanceof Error ? error.message : "未知错误"));
        } finally {
            setIsLoading(false);
        }
    };

    const stopAllTasks = async () => {
        setIsLoading(true);
        try {
            // 停止所有运行中的任务
            const runningTasks = tasks.filter((task) => task.status === "running");

            for (const task of runningTasks) {
                await electronTradingClient.stopTask(task.id);
            }

            // 重新获取任务列表
            await fetchTasks();
            console.log("已停止所有任务");
        } catch (error) {
            console.error("停止所有任务失败:", error);
            alert("停止任务失败: " + (error instanceof Error ? error.message : "未知错误"));
        } finally {
            setIsLoading(false);
        }
    };

    const toggleTask = async (taskId: string) => {
        try {
            const task = tasks.find((t) => t.id === taskId);
            if (!task) {
                throw new Error("任务不存在");
            }

            if (task.status === "running") {
                await electronTradingClient.stopTask(taskId);
            } else if (task.status === "paused" || task.status === "stopped") {
                await electronTradingClient.startTask(taskId);
            } else {
                throw new Error("任务状态错误，无法切换");
            }

            // 重新获取任务列表
            await fetchTasks();

            const action = task.status === "running" ? "停止" : "启动";
            console.log(`任务 ${task.name} 已${action}`);
        } catch (error) {
            console.error("切换任务状态失败:", error);
            alert("操作失败: " + (error instanceof Error ? error.message : "未知错误"));
        }
    };

    const addTask = async (taskConfig: Partial<Task>) => {
        setIsLoading(true);
        try {
            // 转换为 TaskCreateOptions 格式，处理类型兼容性
            const createOptions: TaskCreateOptions = {
                name: taskConfig.name || "未命名任务",
                stockCode: taskConfig.stockCode || "",
                stockName: taskConfig.stockName || "",
                market: (taskConfig as any).market || ("HK" as Market),
                strategyConfig: {
                    strategyType: (taskConfig.strategyConfig?.strategyType as StrategyType) || ("strategy_a_big_order_monitor" as StrategyType),
                    params: taskConfig.strategyConfig?.params || {},
                    requiredDataTypes: []
                },
                riskConfig: {
                    triggerLogic: taskConfig.riskConfig?.triggerLogic || "any",
                    conditions:
                        taskConfig.riskConfig?.conditions?.map((condition) => ({
                            id: condition.id,
                            type: condition.type as any, // 类型转换
                            params: condition.params
                        })) || [],
                    liquidationStrategy: {
                        type: (taskConfig.riskConfig?.liquidationStrategy?.type as LiquidationType) || ("market" as LiquidationType),
                        params: taskConfig.riskConfig?.liquidationStrategy?.params || {}
                    }
                },
                autoStart: false
            };

            const response = await electronTradingClient.createTask(createOptions);
            if (response.success) {
                // 重新获取任务列表
                await fetchTasks();
                console.log("已添加新任务:", createOptions.name);
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            console.error("添加任务失败:", error);
            alert("添加任务失败: " + (error instanceof Error ? error.message : "未知错误"));
        } finally {
            setIsLoading(false);
        }
    };

    const updateTask = async (taskId: string, updates: Partial<Task>) => {
        try {
            // Electron版本暂时不支持更新任务，可以先删除再创建
            console.warn("任务更新功能暂未实现，建议删除后重新创建");
            alert("任务更新功能暂未实现，请删除任务后重新创建");
        } catch (error) {
            console.error("更新任务失败:", error);
            alert("更新任务失败: " + (error instanceof Error ? error.message : "未知错误"));
        }
    };

    const deleteTask = async (taskId: string) => {
        const task = tasks.find((t) => t.id === taskId);
        if (!task) {
            throw new Error("任务不存在或已被删除");
        }

        if (task.status === "running") {
            throw new Error("无法删除运行中的任务，请先停止任务");
        }

        setIsLoading(true);
        try {
            const response = await electronTradingClient.deleteTask(taskId);
            if (response.success) {
                // 重新获取任务列表
                await fetchTasks();
                console.log(`任务 ${task.name} 已删除`);
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            console.error("删除任务失败:", error);
            throw error; // 重新抛出错误让上层处理
        } finally {
            setIsLoading(false);
        }
    };

    const liquidateTask = async (taskId: string) => {
        const task = tasks.find((t) => t.id === taskId);
        if (!task) {
            throw new Error("任务不存在");
        }

        if (task.status !== "running") {
            throw new Error("只有运行中的任务可以清仓");
        }

        if (task.position <= 0) {
            throw new Error("该任务没有持仓");
        }

        setIsLoading(true);
        try {
            // 暂时使用停止任务代替清仓功能
            const response = await electronTradingClient.stopTask(taskId);
            if (response.success) {
                // 重新获取任务列表
                await fetchTasks();
                console.log(`任务 ${task.name} 已停止（代替清仓）`);
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            console.error("停止任务失败:", error);
            throw error;
        } finally {
            setIsLoading(false);
        }
    };

    const copyTask = async (taskId: string) => {
        const sourceTask = tasks.find((t) => t.id === taskId);
        if (!sourceTask) {
            throw new Error("源任务不存在");
        }

        // 生成唯一的任务名称
        const generateUniqueName = (baseName: string): string => {
            const copyName = `${baseName}_副本`;
            const existingNames = tasks.map((t) => t.name);

            if (!existingNames.includes(copyName)) {
                return copyName;
            }

            // 如果已存在，添加数字后缀
            let counter = 1;
            let uniqueName = `${copyName}${counter}`;
            while (existingNames.includes(uniqueName)) {
                counter++;
                uniqueName = `${copyName}${counter}`;
            }
            return uniqueName;
        };

        // 创建复制的任务配置（排除运行时状态）
        const copiedTask = {
            name: generateUniqueName(sourceTask.name),
            stockCode: sourceTask.stockCode,
            stockName: sourceTask.stockName,
            strategyConfig: JSON.parse(JSON.stringify(sourceTask.strategyConfig)), // 深拷贝
            riskConfig: JSON.parse(JSON.stringify(sourceTask.riskConfig)) // 深拷贝
        };

        // 打开配置模态框，预填充复制的数据
        setSelectedTask({
            ...sourceTask,
            ...copiedTask,
            id: "temp-copy",
            status: "stopped",
            position: 0,
            pnl: 0,
            avgCost: undefined,
            createdAt: new Date(),
            updatedAt: new Date(),
            isActive: false
        } as Task);
        setShowConfigModal(true);

        console.log(`已准备复制任务: ${sourceTask.name} -> ${copiedTask.name}`);
    };

    const getTaskLogs = async (taskId: string): Promise<LogEntry[]> => {
        const task = tasks.find((t) => t.id === taskId);
        if (!task) {
            throw new Error("任务不存在");
        }

        // 模拟API调用延迟
        await new Promise((resolve) => setTimeout(resolve, 500));

        // 生成模拟日志数据（因为Electron后端暂时没有实现日志功能）
        const generateMockLogs = (task: Task): LogEntry[] => {
            const logs: LogEntry[] = [];
            const now = new Date();

            // 生成一些基本的系统日志
            const systemLogs = [
                { level: "info" as const, message: `任务 ${task.name} 初始化完成` },
                { level: "info" as const, message: `策略类型: ${task.strategyConfig.strategyType}` },
                { level: "info" as const, message: `目标股票: ${task.stockCode} (${task.stockName})` },
                { level: "info" as const, message: `当前状态: ${task.status}` },
                { level: "info" as const, message: `持仓数量: ${task.position} 股` },
                { level: "info" as const, message: `浮动盈亏: ${task.pnl.toFixed(2)} HKD` }
            ];

            systemLogs.forEach((log, index) => {
                logs.push({
                    id: `log-${taskId}-${index}`,
                    timestamp: new Date(now.getTime() - (systemLogs.length - index) * 60000),
                    level: log.level,
                    category: "system",
                    message: log.message,
                    taskId,
                    taskName: task.name
                });
            });

            return logs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
        };

        const mockLogs = generateMockLogs(task);
        console.log(`获取任务 ${task.name} 的日志，共 ${mockLogs.length} 条记录`);
        return mockLogs;
    };

    // Modal 控制函数
    const openConfigModal = (task?: Task) => {
        setSelectedTask(task || null);
        setShowConfigModal(true);
    };

    const closeConfigModal = () => {
        setSelectedTask(null);
        setShowConfigModal(false);
    };

    const openDetailsModal = (taskId: string) => {
        const task = tasks.find((t) => t.id === taskId);
        if (task) {
            setSelectedTask(task);
            setShowDetailsModal(true);
        }
    };

    const closeDetailsModal = () => {
        setSelectedTask(null);
        setShowDetailsModal(false);
    };

    const editTask = (taskId: string) => {
        const task = tasks.find((t) => t.id === taskId);
        if (task) {
            if (task.status === "running") {
                alert("请先暂停任务后再进行编辑");
                return;
            }
            openConfigModal(task);
        }
    };

    const saveTaskConfig = async (taskConfig: Partial<Task>) => {
        setIsLoading(true);
        try {
            if (selectedTask && selectedTask.id !== "temp-copy") {
                // 编辑现有任务（暂时不支持）
                await updateTask(selectedTask.id, taskConfig);
                console.log(`任务 ${taskConfig.name || selectedTask.name} 已更新`);
            } else {
                // 添加新任务（包括复制的任务）
                await addTask(taskConfig);
                if (selectedTask?.id === "temp-copy") {
                    console.log(`任务复制成功: ${taskConfig.name}`);
                }
            }
            closeConfigModal();
        } catch (error) {
            console.error("保存任务配置失败:", error);
            alert("保存失败: " + (error instanceof Error ? error.message : "未知错误"));
        } finally {
            setIsLoading(false);
        }
    };

    // 设置事件监听器
    useEffect(() => {
        // 监听系统心跳
        electronTradingClient.onSystemHeartbeat((data) => {
            if (data) {
                setGlobalStatus((prev) => {
                    // 优先使用ServiceManager的详细状态，避免心跳覆盖实时状态
                    let marketConnected = prev.connections.market === "connected";
                    let tradingConnected = prev.connections.trading === "connected";

                    if (data.services) {
                        // 从ServiceManager获取精确状态
                        const marketService = data.services.marketData;
                        const tradingService = data.services.trading;

                        // 只有当服务启用且连接成功时才显示为connected
                        marketConnected = marketService ? marketService.enabled && marketService.connected && marketService.status === "running" : false;
                        tradingConnected = tradingService ? tradingService.enabled && tradingService.connected && tradingService.status === "running" : false;
                    } else {
                        // 回退到传统状态，但不覆盖已有的连接状态
                        marketConnected = data.marketConnected || marketConnected;
                        tradingConnected = data.tradingConnected || tradingConnected;
                    }

                    return {
                        ...prev,
                        tradingSystemStatus: data,
                        connections: {
                            market: marketConnected ? "connected" : "disconnected",
                            trading: tradingConnected ? "connected" : "disconnected"
                        }
                    };
                });
            }
        });

        // 监听连接状态变化（实时推送）
        electronTradingClient.onConnectionStatus((data) => {
            console.log("连接状态变化:", JSON.stringify(data, null, 2));

            // 立即更新连接状态
            if (data && data.service) {
                setGlobalStatus((prev) => {
                    const newConnections = { ...prev.connections };

                    // 根据服务状态决定连接状态
                    if (data.service === "marketData") {
                        if (data.status === "disabled") {
                            newConnections.market = "disconnected"; // 禁用的服务显示为断开
                        } else if (data.status === "initializing") {
                            newConnections.market = "connecting"; // 初始化中显示为连接中
                        } else {
                            newConnections.market = data.connected ? "connected" : "disconnected";
                        }
                    } else if (data.service === "trading") {
                        if (data.status === "disabled") {
                            newConnections.trading = "disconnected"; // 禁用的服务显示为断开
                        } else if (data.status === "initializing") {
                            newConnections.trading = "connecting"; // 初始化中显示为连接中
                        } else {
                            newConnections.trading = data.connected ? "connected" : "disconnected";
                        }
                    }

                    return {
                        ...prev,
                        connections: newConnections
                    };
                });
            }
        });

        // 监听系统状态变化（实时推送）
        electronTradingClient.onSystemStatus((data) => {
            console.log("系统状态变化:", JSON.stringify(data, null, 2));

            if (data && data.services) {
                setGlobalStatus((prev) => {
                    const newConnections = { ...prev.connections };

                    // 从系统状态更新连接状态
                    const marketService = data.services.marketData;
                    const tradingService = data.services.trading;

                    if (marketService) {
                        if (!marketService.enabled || marketService.status === "disabled") {
                            newConnections.market = "disconnected";
                        } else if (marketService.status === "initializing") {
                            newConnections.market = "connecting";
                        } else {
                            newConnections.market = marketService.connected && marketService.status === "running" ? "connected" : "disconnected";
                        }
                    }

                    if (tradingService) {
                        if (!tradingService.enabled || tradingService.status === "disabled") {
                            newConnections.trading = "disconnected";
                        } else if (tradingService.status === "initializing") {
                            newConnections.trading = "connecting";
                        } else {
                            newConnections.trading = tradingService.connected && tradingService.status === "running" ? "connected" : "disconnected";
                        }
                    }

                    return {
                        ...prev,
                        connections: newConnections,
                        tradingSystemStatus: prev.tradingSystemStatus
                            ? {
                                  ...prev.tradingSystemStatus,
                                  services: data.services
                              }
                            : {
                                  initialized: false,
                                  marketConnected: false,
                                  tradingConnected: false,
                                  activeTasksCount: 0,
                                  lastUpdate: Date.now(),
                                  services: data.services
                              }
                    };
                });
            }
        });

        // 监听错误事件
        electronTradingClient.onError((data) => {
            console.error("系统错误:", data);
        });

        // 初始化数据（仅获取一次，后续依赖事件推送）
        const initializeData = async () => {
            await fetchSystemStatus();
            await fetchTasks();
        };

        initializeData();

        // 清理函数
        return () => {
            electronTradingClient.destroy();
        };
    }, [fetchTasks, fetchSystemStatus]);

    // 备用轮询机制（降低频率，主要依赖事件推送）
    useEffect(() => {
        const interval = setInterval(async () => {
            // 只更新任务列表，状态更新主要依赖事件推送
            await fetchTasks();

            // 备用状态检查（频率大幅降低）
            await fetchSystemStatus();
        }, 30000); // 每30秒备用检查一次

        return () => clearInterval(interval);
    }, [fetchSystemStatus, fetchTasks]);

    return {
        // 状态
        tasks,
        globalStatus,
        isLoading,
        selectedTask,
        showConfigModal,
        showDetailsModal,

        // 操作函数
        startAllTasks,
        stopAllTasks,
        addTask,
        updateTask,
        deleteTask,
        toggleTask,
        liquidateTask,
        copyTask,
        getTaskLogs,

        // Modal 控制
        openConfigModal,
        closeConfigModal,
        openDetailsModal,
        closeDetailsModal,

        // 配置相关
        saveTaskConfig,
        editTask
    };
};
