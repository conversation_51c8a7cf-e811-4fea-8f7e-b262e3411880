/**
 * 行情数据管理器
 * ===============
 * 统一管理所有任务的行情数据订阅，实现数据共享和引用计数管理
 * 避免重复订阅同一股票的数据，优化性能和资源使用
 */

import { EventEmitter } from "events";
import { FutuMarketAdapter } from "./FutuMarketAdapter";
import { Market, DataType, RealtimeData, SubscriptionInfo, MarketAdapter, FutuMarketConfig, ConnectionStatus } from "./market-types";

// 订阅引用信息
interface SubscriptionReference {
    stockCode: string;
    market: Market;
    dataTypes: DataType[];
    refCount: number; // 引用计数
    taskIds: Set<string>; // 使用此订阅的任务ID集合
    subscriptionId?: string; // SDK返回的订阅ID
    isActive: boolean; // 是否激活
    lastUpdateTime: number; // 最后更新时间
}

// 任务订阅记录
interface TaskSubscription {
    taskId: string;
    subscriptions: Map<string, SubscriptionReference>; // key: subscriptionKey
}

// 缓存数据
interface CachedData {
    data: RealtimeData;
    expireTime: number;
}

export class MarketDataManager extends EventEmitter {
    private adapter: MarketAdapter;
    private subscriptions: Map<string, SubscriptionReference> = new Map(); // 所有订阅
    private taskSubscriptions: Map<string, Set<string>> = new Map(); // taskId -> subscriptionKeys
    private dataCache: Map<string, CachedData> = new Map(); // 数据缓存
    private isConnected: boolean = false;
    private config: FutuMarketConfig | null = null;

    // 配置参数
    private readonly CACHE_DURATION = 30000; // 缓存30秒
    private readonly MAX_CACHE_SIZE = 10000; // 最大缓存条目
    private cacheCleanupTimer: NodeJS.Timeout | null = null;

    constructor() {
        super();
        this.adapter = new FutuMarketAdapter();
        this.setupAdapterEvents();
        this.startCacheCleanup();
    }

    /**
     * 设置适配器事件监听
     */
    private setupAdapterEvents() {
        this.adapter.on("connected", () => {
            this.isConnected = true;
            this.emit("connected");
            console.log("[MarketDataManager] 连接成功");
        });

        this.adapter.on("disconnected", () => {
            this.isConnected = false;
            this.emit("disconnected");
            console.log("[MarketDataManager] 连接断开");
        });

        this.adapter.on("error", (error) => {
            this.emit("error", error);
            console.error("[MarketDataManager] 适配器错误:", error);
        });

        this.adapter.on("data", (data: RealtimeData) => {
            this.handleRealtimeData(data);
        });
    }

    /**
     * 连接到行情服务器
     */
    async connect(config: FutuMarketConfig): Promise<void> {
        try {
            this.config = config;
            console.log("[MarketDataManager] 尝试连接行情服务器...", {
                host: config.host,
                port: config.port
            });

            await this.adapter.connect(config);

            // 等待adapter确认连接成功
            if (!this.adapter.isConnected()) {
                throw new Error("适配器连接失败");
            }

            this.isConnected = true;
            console.log("[MarketDataManager] 连接成功");
            this.emit("connected");
        } catch (error) {
            console.error("[MarketDataManager] 连接失败:", error);
            this.isConnected = false;
            this.emit("connection-failed", error);
            throw error;
        }
    }

    /**
     * 尝试重新连接
     */
    async reconnect(): Promise<void> {
        if (!this.config) {
            throw new Error("没有可用的配置信息");
        }

        console.log("[MarketDataManager] 尝试重新连接...");
        try {
            await this.disconnect();
            await this.connect(this.config);

            // 重新订阅所有活跃的订阅
            await this.resubscribeAll();
        } catch (error) {
            console.error("[MarketDataManager] 重连失败:", error);
            throw error;
        }
    }

    /**
     * 断开连接
     */
    async disconnect(): Promise<void> {
        try {
            if (this.cacheCleanupTimer) {
                clearInterval(this.cacheCleanupTimer);
                this.cacheCleanupTimer = null;
            }

            await this.adapter.disconnect();
            this.subscriptions.clear();
            this.taskSubscriptions.clear();
            this.dataCache.clear();
            this.isConnected = false;
        } catch (error) {
            console.error("[MarketDataManager] 断开连接失败:", error);
            throw error;
        }
    }

    /**
     * 为任务订阅数据
     */
    async subscribeForTask(taskId: string, stockCode: string, market: Market = Market.HK, dataTypes: DataType[]): Promise<void> {
        if (!this.isConnected) {
            throw new Error("未连接到行情服务器");
        }

        try {
            const subscriptionKey = this.getSubscriptionKey(stockCode, market, dataTypes);

            // 记录任务订阅
            if (!this.taskSubscriptions.has(taskId)) {
                this.taskSubscriptions.set(taskId, new Set());
            }
            this.taskSubscriptions.get(taskId)!.add(subscriptionKey);

            const subscription = this.subscriptions.get(subscriptionKey);

            if (subscription) {
                // 已有订阅，增加引用计数
                subscription.refCount++;
                subscription.taskIds.add(taskId);
                console.log(`[MarketDataManager] 增加引用: ${subscriptionKey}, 引用数: ${subscription.refCount}`);
            } else {
                // 新建订阅
                await this.createSubscription(stockCode, market, dataTypes, taskId, subscriptionKey);
            }
        } catch (error) {
            console.error("[MarketDataManager] 订阅失败:", error);
            throw error;
        }
    }

    /**
     * 取消任务订阅
     */
    async unsubscribeForTask(taskId: string, stockCode: string, market: Market = Market.HK, dataTypes: DataType[]): Promise<void> {
        try {
            const subscriptionKey = this.getSubscriptionKey(stockCode, market, dataTypes);
            const subscription = this.subscriptions.get(subscriptionKey);

            if (subscription) {
                subscription.refCount--;
                subscription.taskIds.delete(taskId);

                console.log(`[MarketDataManager] 减少引用: ${subscriptionKey}, 引用数: ${subscription.refCount}`);

                // 如果没有任务使用这个订阅，则取消订阅
                if (subscription.refCount <= 0) {
                    await this.removeSubscription(stockCode, market, dataTypes, subscriptionKey);
                }
            }

            // 清理任务记录
            const taskSubs = this.taskSubscriptions.get(taskId);
            if (taskSubs) {
                taskSubs.delete(subscriptionKey);
                if (taskSubs.size === 0) {
                    this.taskSubscriptions.delete(taskId);
                }
            }
        } catch (error) {
            console.error("[MarketDataManager] 取消订阅失败:", error);
            throw error;
        }
    }

    /**
     * 清理任务的所有订阅
     */
    async cleanupTaskSubscriptions(taskId: string): Promise<void> {
        const taskSubs = this.taskSubscriptions.get(taskId);
        if (!taskSubs) return;

        try {
            // 逐个取消任务的所有订阅
            for (const subscriptionKey of taskSubs) {
                const subscription = this.subscriptions.get(subscriptionKey);
                if (subscription) {
                    subscription.refCount--;
                    subscription.taskIds.delete(taskId);

                    // 如果没有其他任务使用，则完全取消订阅
                    if (subscription.refCount <= 0) {
                        await this.adapter.unsubscribe(subscription.stockCode, subscription.market, subscription.dataTypes);
                        this.subscriptions.delete(subscriptionKey);
                        console.log(`[MarketDataManager] 完全取消订阅: ${subscriptionKey}`);
                    }
                }
            }

            // 清理任务记录
            this.taskSubscriptions.delete(taskId);
            console.log(`[MarketDataManager] 清理任务订阅: ${taskId}`);
        } catch (error) {
            console.error("[MarketDataManager] 清理任务订阅失败:", error);
            throw error;
        }
    }

    /**
     * 获取任务的实时数据
     */
    getRealtimeDataForTask(taskId: string, stockCode: string, market: Market = Market.HK): RealtimeData | null {
        const dataKey = `${market}_${stockCode}`;
        const cached = this.dataCache.get(dataKey);

        if (cached && cached.expireTime > Date.now()) {
            return cached.data;
        }

        return null;
    }

    /**
     * 获取所有缓存的数据
     */
    getAllCachedData(): RealtimeData[] {
        const now = Date.now();
        const result: RealtimeData[] = [];

        for (const [key, cached] of this.dataCache.entries()) {
            if (cached.expireTime > now) {
                result.push(cached.data);
            }
        }

        return result;
    }

    /**
     * 创建新订阅
     */
    private async createSubscription(stockCode: string, market: Market, dataTypes: DataType[], taskId: string, subscriptionKey: string): Promise<void> {
        try {
            const subscriptionId = await this.adapter.subscribe(stockCode, market, dataTypes);

            const subscription: SubscriptionReference = {
                stockCode,
                market,
                dataTypes,
                refCount: 1,
                taskIds: new Set([taskId]),
                subscriptionId,
                isActive: true,
                lastUpdateTime: Date.now()
            };

            this.subscriptions.set(subscriptionKey, subscription);
            console.log(`[MarketDataManager] 创建新订阅: ${subscriptionKey}`);
        } catch (error) {
            console.error("[MarketDataManager] 创建订阅失败:", error);
            throw error;
        }
    }

    /**
     * 移除订阅
     */
    private async removeSubscription(stockCode: string, market: Market, dataTypes: DataType[], subscriptionKey: string): Promise<void> {
        try {
            await this.adapter.unsubscribe(stockCode, market, dataTypes);
            this.subscriptions.delete(subscriptionKey);
            console.log(`[MarketDataManager] 移除订阅: ${subscriptionKey}`);
        } catch (error) {
            console.error("[MarketDataManager] 移除订阅失败:", error);
            throw error;
        }
    }

    /**
     * 处理实时数据
     */
    private handleRealtimeData(data: RealtimeData) {
        // 更新缓存
        const dataKey = `${data.market}_${data.stockCode}`;
        this.dataCache.set(dataKey, {
            data,
            expireTime: Date.now() + this.CACHE_DURATION
        });

        // 找出所有订阅这个股票的任务
        const affectedTasks = new Set<string>();

        for (const [subscriptionKey, subscription] of this.subscriptions.entries()) {
            if (subscription.stockCode === data.stockCode && subscription.market === data.market && subscription.dataTypes.includes(data.type)) {
                subscription.lastUpdateTime = Date.now();
                subscription.taskIds.forEach((taskId) => affectedTasks.add(taskId));
            }
        }

        // 通知相关任务
        if (affectedTasks.size > 0) {
            this.emit("taskData", {
                taskIds: Array.from(affectedTasks),
                stockCode: data.stockCode,
                market: data.market,
                data
            });
        }

        // 发送原始数据事件
        this.emit("data", data);
    }

    /**
     * 生成订阅Key
     */
    private getSubscriptionKey(stockCode: string, market: Market, dataTypes: DataType[]): string {
        const sortedTypes = [...dataTypes].sort();
        return `${market}_${stockCode}_${sortedTypes.join(",")}`;
    }

    /**
     * 启动缓存清理定时器
     */
    private startCacheCleanup() {
        this.cacheCleanupTimer = setInterval(() => {
            this.cleanupExpiredCache();
        }, 60000); // 每分钟清理一次
    }

    /**
     * 清理过期缓存
     */
    private cleanupExpiredCache() {
        const now = Date.now();
        let cleanedCount = 0;

        for (const [key, cached] of this.dataCache.entries()) {
            if (cached.expireTime <= now) {
                this.dataCache.delete(key);
                cleanedCount++;
            }
        }

        // 如果缓存超过最大限制，清理最旧的数据
        if (this.dataCache.size > this.MAX_CACHE_SIZE) {
            const entries = Array.from(this.dataCache.entries());
            entries.sort((a, b) => a[1].expireTime - b[1].expireTime);

            const toRemove = entries.slice(0, this.dataCache.size - this.MAX_CACHE_SIZE);
            for (const [key] of toRemove) {
                this.dataCache.delete(key);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            console.log(`[MarketDataManager] 清理缓存: ${cleanedCount} 条记录`);
        }
    }

    /**
     * 获取统计信息
     */
    getStatistics() {
        const now = Date.now();
        const validCacheCount = Array.from(this.dataCache.values()).filter((cached) => cached.expireTime > now).length;

        return {
            totalSubscriptions: this.subscriptions.size,
            activeTasks: this.taskSubscriptions.size,
            cacheSize: validCacheCount,
            isConnected: this.isConnected,
            lastUpdateTime: Math.max(
                ...Array.from(this.subscriptions.values())
                    .map((sub) => sub.lastUpdateTime)
                    .filter(Boolean),
                0
            )
        };
    }

    /**
     * 获取所有活跃订阅
     */
    getActiveSubscriptions(): SubscriptionReference[] {
        return Array.from(this.subscriptions.values()).filter((sub) => sub.isActive);
    }

    /**
     * 获取任务的订阅信息
     */
    getTaskSubscriptions(taskId: string): SubscriptionReference[] {
        const taskSubs = this.taskSubscriptions.get(taskId);
        if (!taskSubs) return [];

        return Array.from(taskSubs)
            .map((key) => this.subscriptions.get(key))
            .filter(Boolean) as SubscriptionReference[];
    }

    /**
     * 检查连接状态
     */
    isAdapterConnected(): boolean {
        return this.isConnected && this.adapter.isConnected();
    }

    /**
     * 重新订阅所有活跃订阅
     */
    private async resubscribeAll(): Promise<void> {
        console.log("[MarketDataManager] 重新订阅所有数据...");

        for (const [key, subscription] of this.subscriptions.entries()) {
            if (subscription.isActive && subscription.refCount > 0) {
                try {
                    const newSubId = await this.adapter.subscribe(subscription.stockCode, subscription.market, subscription.dataTypes);
                    subscription.subscriptionId = newSubId;
                    console.log(`[MarketDataManager] 重新订阅成功: ${key}`);
                } catch (error) {
                    console.error(`[MarketDataManager] 重新订阅失败: ${key}`, error);
                }
            }
        }
    }
}
