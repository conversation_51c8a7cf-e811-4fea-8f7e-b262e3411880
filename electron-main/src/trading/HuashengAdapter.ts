/**
 * 华盛通交易适配器
 * ==================
 * 基于华盛通 ProApi 接口实现的交易适配器
 * 提供账户管理、订单执行、持仓查询等功能
 */

import { EventEmitter } from "events";
import { HuashengTcpClient } from "./HuashengTcpClient";
import {
    HuashengConfig,
    ConnectionStatus,
    LoginRequest,
    LoginResponse,
    FundsResponse,
    PositionResponse,
    EntrustRequest,
    EntrustResponse,
    PlaceOrderRequest,
    PlaceOrderResponse,
    UnOrderRequest,
    UnOrderResponse,
    Position,
    Order,
    ApiRequestType,
    Side,
    OrderType,
    OrderStatus
} from "./types";

export interface TradingAdapter {
    // 连接管理
    connect(config?: any): Promise<void>;
    disconnect(): Promise<void>;
    isConnected(): boolean;

    // 账户管理
    login(account: string, password: string): Promise<void>;
    getAccountInfo(): Promise<any>;

    // 资金查询
    getFunds(): Promise<any>;

    // 持仓管理
    getPositions(): Promise<Position[]>;

    // 订单管理
    getOrders(filter?: any): Promise<Order[]>;
    placeOrder(order: any): Promise<any>;
    cancelOrder(orderId: string): Promise<void>;

    // 状态查询
    getStatus(): any;
}

export class HuashengAdapter extends EventEmitter implements TradingAdapter {
    private tcpClient: HuashengTcpClient;
    private token: string | null = null;
    private isLoggedIn: boolean = false;
    private config: HuashengConfig;

    constructor(config: HuashengConfig) {
        super();
        this.config = config;
        this.tcpClient = new HuashengTcpClient(config);

        // 监听连接事件
        this.tcpClient.on("connected", () => {
            this.emit("connected");
        });

        this.tcpClient.on("disconnected", (hadError: boolean) => {
            this.isLoggedIn = false;
            this.token = null;
            this.emit("disconnected", hadError);
        });

        this.tcpClient.on("error", (error: Error) => {
            this.emit("error", error);
        });

        this.tcpClient.on("push", (data: any) => {
            this.handlePushData(data);
        });
    }

    /**
     * 连接到华盛通服务器
     */
    async connect(config?: Partial<HuashengConfig>): Promise<void> {
        if (config) {
            this.config = { ...this.config, ...config };
        }

        try {
            await this.tcpClient.connect();
            console.log("华盛通适配器连接成功");
        } catch (error) {
            console.error("华盛通适配器连接失败:", error);
            throw error;
        }
    }

    /**
     * 断开连接
     */
    async disconnect(): Promise<void> {
        try {
            await this.tcpClient.disconnect();
            this.isLoggedIn = false;
            this.token = null;
            console.log("华盛通适配器已断开连接");
        } catch (error) {
            console.error("华盛通适配器断开连接失败:", error);
            throw error;
        }
    }

    /**
     * 检查连接状态
     */
    isConnected(): boolean {
        return this.tcpClient.getConnectionStatus().connected;
    }

    /**
     * 用户登录
     */
    async login(account?: string, password?: string): Promise<void> {
        const loginAccount = account || this.config.account;
        const loginPassword = password || this.config.password;

        if (!loginAccount || !loginPassword) {
            throw new Error("账户或密码未设置");
        }

        const request: LoginRequest = {
            RequestId: 0, // 将由客户端设置
            RequestType: ApiRequestType.Login,
            Account: loginAccount,
            Password: loginPassword
        };

        try {
            const response = await this.tcpClient.sendRequest<LoginResponse>(request);

            if (response.LoginResoult && response.Token) {
                this.token = response.Token;
                this.isLoggedIn = true;
                console.log("华盛通登录成功");
                this.emit("login-success", { account: loginAccount });
            } else {
                throw new Error("登录失败: " + response.ResponseMsg);
            }
        } catch (error) {
            console.error("华盛通登录失败:", error);
            this.emit("login-error", error);
            throw error;
        }
    }

    /**
     * 获取账户信息
     */
    async getAccountInfo(): Promise<any> {
        this.ensureLoggedIn();

        return {
            account: this.config.account,
            loginStatus: this.isLoggedIn,
            connectionStatus: this.isConnected(),
            loginTime: Date.now() // 实际应该记录登录时间
        };
    }

    /**
     * 查询资金信息
     */
    async getFunds(): Promise<any> {
        this.ensureLoggedIn();

        const request = {
            RequestId: 0,
            RequestType: ApiRequestType.QueryFunds,
            Token: this.token!
        };

        try {
            console.log("发送资金查询请求:", request);
            const response = await this.tcpClient.sendRequest<FundsResponse>(request);

            // 检查响应数据结构
            if (!response.Payload) {
                console.warn("资金查询响应缺少Payload数据:", response);
                return {
                    error: "响应数据格式错误",
                    rawResponse: response,
                    timestamp: Date.now()
                };
            }

            const fundsData = {
                // 使用实际API返回的字段
                dayNetProfit: response.Payload.dayNetProfit,
                buyingPower: response.Payload.buyingPower,
                dealerAccountId: response.Payload.dealerAccountId,
                frozenBalance: response.Payload.frozenBalance,
                dayClosePositionProfit: response.Payload.dayClosePositionProfit,
                enableBalance: response.Payload.enableBalance,
                dayMarketValue: response.Payload.dayMarketValue,
                prevMarketValue: response.Payload.prevMarketValue,

                // 提供标准化的字段名以保持兼容性
                availableFunds: response.Payload.enableBalance || response.Payload.AvailableFunds,
                buyingPowerStandard: response.Payload.buyingPower || response.Payload.BuyingPower,
                totalAssets: (response.Payload.enableBalance || 0) + (response.Payload.dayMarketValue || 0) || response.Payload.TotalAssets,
                frozenFunds: response.Payload.frozenBalance || response.Payload.FrozenFunds,
                currency: response.Payload.Currency || "HKD", // 默认港币
                timestamp: Date.now()
            };

            console.log("收到资金查询响应:", fundsData);

            return fundsData;
        } catch (error) {
            console.error("查询资金信息失败:", error);
            throw error;
        }
    }

    /**
     * 查询持仓信息
     */
    async getPositions(): Promise<Position[]> {
        this.ensureLoggedIn();

        const request = {
            RequestId: 0,
            RequestType: ApiRequestType.QueryPosition,
            Token: this.token!
        };

        try {
            const response = await this.tcpClient.sendRequest<PositionResponse>(request);
            return response.Payload || [];
        } catch (error) {
            console.error("查询持仓信息失败:", error);
            throw error;
        }
    }

    /**
     * 查询订单列表
     */
    async getOrders(filter?: { pageNo?: number; pageSize?: number }): Promise<Order[]> {
        this.ensureLoggedIn();

        const request: EntrustRequest = {
            RequestId: 0,
            RequestType: ApiRequestType.QueryEntrust,
            Token: this.token!,
            ...filter
        };

        try {
            const response = await this.tcpClient.sendRequest<EntrustResponse>(request);
            return response.Payload || [];
        } catch (error) {
            console.error("查询订单列表失败:", error);
            throw error;
        }
    }

    /**
     * 下单
     */
    async placeOrder(orderParams: { stockCode: string; price: number; quantity: number; side: "BUY" | "SELL"; orderType?: "LIMIT" | "MARKET" }): Promise<any> {
        this.ensureLoggedIn();

        // 转换参数
        const side = orderParams.side === "BUY" ? Side.Buy : Side.Sell;
        const orderType = orderParams.orderType === "MARKET" ? OrderType.Market : OrderType.Limited;

        const request: PlaceOrderRequest = {
            RequestId: 0,
            RequestType: ApiRequestType.PlaceOrder,
            Token: this.token!,
            StockCode: orderParams.stockCode,
            Price: orderParams.price,
            Qty: orderParams.quantity,
            Side: side,
            OrderType: orderType
        };

        try {
            const response = await this.tcpClient.sendRequest<PlaceOrderResponse>(request);

            return {
                orderId: response.Payload.OrderId,
                entrustNo: response.Payload.EntrustNo,
                status: response.Payload.Status,
                stockCode: orderParams.stockCode,
                price: orderParams.price,
                quantity: orderParams.quantity,
                side: orderParams.side,
                timestamp: Date.now()
            };
        } catch (error) {
            console.error("下单失败:", error);
            throw error;
        }
    }

    /**
     * 撤单
     */
    async cancelOrder(entrustNo: string): Promise<void> {
        this.ensureLoggedIn();

        const request: UnOrderRequest = {
            RequestId: 0,
            RequestType: ApiRequestType.UnOrder,
            Token: this.token!,
            EntrustNo: entrustNo
        };

        try {
            const response = await this.tcpClient.sendRequest<UnOrderResponse>(request);
            console.log("撤单成功:", response.Payload);
        } catch (error) {
            console.error("撤单失败:", error);
            throw error;
        }
    }

    /**
     * 处理推送数据
     */
    private handlePushData(data: any): void {
        console.log("收到华盛通推送数据:", data);

        // 根据推送类型处理数据
        switch (data.ResponseType) {
            case 101: // 订单推送
                this.emit("order-update", data.Payload);
                break;
            case 10001: // 统计数据推送
                this.emit("statistics-update", data.Payload);
                break;
            default:
                this.emit("push-data", data);
        }
    }

    /**
     * 确保已登录
     */
    private ensureLoggedIn(): void {
        if (!this.isLoggedIn || !this.token) {
            throw new Error("请先登录华盛通账户");
        }

        if (!this.isConnected()) {
            throw new Error("华盛通连接已断开");
        }
    }

    /**
     * 获取适配器状态
     */
    getStatus(): {
        connected: boolean;
        loggedIn: boolean;
        account: string;
        connectionStatus: ConnectionStatus;
    } {
        return {
            connected: this.isConnected(),
            loggedIn: this.isLoggedIn,
            account: this.config.account,
            connectionStatus: this.tcpClient.getConnectionStatus()
        };
    }
}
