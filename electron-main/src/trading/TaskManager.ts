/**
 * Electron 任务管理器
 * ==================
 * 统一管理所有策略任务的生命周期、数据订阅、风险控制
 * 与MarketDataManager和TradingManager协作
 */

import { EventEmitter } from "events";
import { MarketDataManager } from "./MarketDataManager";
import { TradingManager } from "./TradingManager";
import { StrategyEngine } from "./StrategyEngine";
import { RiskManager } from "./RiskManager";
import {
    Task,
    TaskStatus,
    TaskEvent,
    TaskEventType,
    TaskStatistics,
    TaskCreateOptions,
    TaskUpdateOptions,
    TaskManagerConfig,
    TaskExecutionContext,
    StrategySignal,
    StrategyExecutionResult,
    RiskCheckResult,
    StrategyConfig,
    RiskConfig,
    RiskConditionType,
    LiquidationType
} from "./task-types";
import { Market, DataType, RealtimeData } from "./market-types";

export class TaskManager extends EventEmitter {
    private tasks: Map<string, Task> = new Map();
    private taskTimers: Map<string, NodeJS.Timeout> = new Map();
    private executionContexts: Map<string, TaskExecutionContext> = new Map();
    private isInitialized: boolean = false;

    // 依赖管理器
    private marketDataManager: MarketDataManager;
    private tradingManager: TradingManager;
    private strategyEngine: StrategyEngine;
    private riskManager: RiskManager;

    // 配置
    private config: TaskManagerConfig = {
        maxConcurrentTasks: 20,
        dataUpdateInterval: 1000, // 1秒
        persistenceEnabled: true,
        riskCheckInterval: 5000 // 5秒
    };

    constructor(marketDataManager: MarketDataManager, tradingManager: TradingManager, strategyEngine?: StrategyEngine, riskManager?: RiskManager, config?: Partial<TaskManagerConfig>) {
        super();
        this.marketDataManager = marketDataManager;
        this.tradingManager = tradingManager;
        this.strategyEngine = strategyEngine || new StrategyEngine();
        this.riskManager = riskManager || new RiskManager();

        if (config) {
            this.config = { ...this.config, ...config };
        }

        this.setupEventHandlers();
    }

    /**
     * 初始化任务管理器
     */
    async initialize(): Promise<void> {
        try {
            console.log("[TaskManager] 正在初始化...");

            // 检查市场数据管理器连接状态
            this.setupMarketDataConnection();

            // 监听市场数据管理器连接状态变化
            this.marketDataManager.on("connected", () => {
                console.log("[TaskManager] MarketDataManager 连接成功，启用行情数据功能");
                this.setupMarketDataConnection();
            });

            this.marketDataManager.on("disconnected", () => {
                console.log("[TaskManager] MarketDataManager 连接断开，切换到无行情数据模式");
            });

            // 启动定时任务
            this.startPeriodicTasks();

            this.isInitialized = true;
            this.emit("initialized");
            console.log("[TaskManager] 初始化完成");
        } catch (error) {
            console.error("[TaskManager] 初始化失败:", error);
            throw error;
        }
    }

    /**
     * 设置事件处理器
     */
    private setupEventHandlers() {
        // 监听进程退出，清理资源
        process.on("SIGINT", () => this.shutdown());
        process.on("SIGTERM", () => this.shutdown());
    }

    /**
     * 创建新任务
     */
    async createTask(options: TaskCreateOptions): Promise<string> {
        if (!this.isInitialized) {
            throw new Error("TaskManager 未初始化");
        }

        if (this.tasks.size >= this.config.maxConcurrentTasks) {
            throw new Error(`任务数量超过限制 (${this.config.maxConcurrentTasks})`);
        }

        try {
            const taskId = this.generateTaskId();
            const now = new Date();

            const task: Task = {
                id: taskId,
                name: options.name,
                stockCode: options.stockCode,
                stockName: options.stockName,
                market: options.market,
                strategyName: this.getStrategyDisplayName(options.strategyConfig),
                status: TaskStatus.Stopped,
                position: 0,
                pnl: 0,
                createdAt: now,
                updatedAt: now,
                strategyConfig: options.strategyConfig,
                riskConfig: options.riskConfig,
                isActive: false
            };

            // 验证策略配置
            this.validateStrategyConfig(task.strategyConfig);

            // 保存任务
            this.tasks.set(taskId, task);

            // 创建执行上下文
            this.executionContexts.set(taskId, {
                task,
                marketData: null,
                currentPrice: 0,
                lastUpdateTime: Date.now()
            });

            // 发送事件
            this.emitTaskEvent({
                type: TaskEventType.Created,
                taskId,
                timestamp: Date.now(),
                data: task
            });

            // 如果需要自动启动
            if (options.autoStart) {
                await this.startTask(taskId);
            }

            console.log(`[TaskManager] 任务创建成功: ${taskId} - ${task.name}`);
            return taskId;
        } catch (error) {
            console.error("[TaskManager] 创建任务失败:", error);
            throw error;
        }
    }

    /**
     * 启动任务
     */
    async startTask(taskId: string): Promise<void> {
        const task = this.tasks.get(taskId);
        if (!task) {
            throw new Error("任务不存在");
        }

        if (task.status === TaskStatus.Running) {
            console.log(`[TaskManager] 任务 ${taskId} 已在运行中`);
            return;
        }

        try {
            // 订阅所需的市场数据
            await this.subscribeMarketData(task);

            // 更新任务状态
            task.status = TaskStatus.Running;
            task.isActive = true;
            task.updatedAt = new Date();

            // 启动策略执行定时器
            this.startTaskExecution(taskId);

            // 发送事件
            this.emitTaskEvent({
                type: TaskEventType.Started,
                taskId,
                timestamp: Date.now()
            });

            console.log(`[TaskManager] 任务启动成功: ${taskId} - ${task.name}`);
        } catch (error) {
            task.status = TaskStatus.Error;
            console.error(`[TaskManager] 启动任务失败 ${taskId}:`, error);
            throw error;
        }
    }

    /**
     * 停止任务
     */
    async stopTask(taskId: string): Promise<void> {
        const task = this.tasks.get(taskId);
        if (!task) {
            throw new Error("任务不存在");
        }

        try {
            // 停止策略执行
            this.stopTaskExecution(taskId);

            // 取消市场数据订阅
            await this.unsubscribeMarketData(task);

            // 更新任务状态
            task.status = TaskStatus.Stopped;
            task.isActive = false;
            task.updatedAt = new Date();

            // 发送事件
            this.emitTaskEvent({
                type: TaskEventType.Stopped,
                taskId,
                timestamp: Date.now()
            });

            console.log(`[TaskManager] 任务停止成功: ${taskId} - ${task.name}`);
        } catch (error) {
            console.error(`[TaskManager] 停止任务失败 ${taskId}:`, error);
            throw error;
        }
    }

    /**
     * 暂停任务
     */
    async pauseTask(taskId: string): Promise<void> {
        const task = this.tasks.get(taskId);
        if (!task) {
            throw new Error("任务不存在");
        }

        if (task.status !== TaskStatus.Running) {
            throw new Error("只能暂停运行中的任务");
        }

        try {
            // 停止策略执行但保持数据订阅
            this.stopTaskExecution(taskId);

            // 更新任务状态
            task.status = TaskStatus.Paused;
            task.updatedAt = new Date();

            // 发送事件
            this.emitTaskEvent({
                type: TaskEventType.Paused,
                taskId,
                timestamp: Date.now()
            });

            console.log(`[TaskManager] 任务暂停成功: ${taskId} - ${task.name}`);
        } catch (error) {
            console.error(`[TaskManager] 暂停任务失败 ${taskId}:`, error);
            throw error;
        }
    }

    /**
     * 恢复任务
     */
    async resumeTask(taskId: string): Promise<void> {
        const task = this.tasks.get(taskId);
        if (!task) {
            throw new Error("任务不存在");
        }

        if (task.status !== TaskStatus.Paused) {
            throw new Error("只能恢复暂停的任务");
        }

        try {
            // 重新启动策略执行
            this.startTaskExecution(taskId);

            // 更新任务状态
            task.status = TaskStatus.Running;
            task.updatedAt = new Date();

            // 发送事件
            this.emitTaskEvent({
                type: TaskEventType.Started,
                taskId,
                timestamp: Date.now()
            });

            console.log(`[TaskManager] 任务恢复成功: ${taskId} - ${task.name}`);
        } catch (error) {
            console.error(`[TaskManager] 恢复任务失败 ${taskId}:`, error);
            throw error;
        }
    }

    /**
     * 删除任务
     */
    async deleteTask(taskId: string): Promise<void> {
        const task = this.tasks.get(taskId);
        if (!task) {
            throw new Error("任务不存在");
        }

        if (task.status === TaskStatus.Running) {
            throw new Error("无法删除运行中的任务，请先停止任务");
        }

        try {
            // 确保任务已停止
            if (task.isActive) {
                await this.stopTask(taskId);
            }

            // 清理资源
            this.cleanupTask(taskId);

            // 删除任务
            this.tasks.delete(taskId);
            this.executionContexts.delete(taskId);

            // 发送事件
            this.emitTaskEvent({
                type: TaskEventType.Deleted,
                taskId,
                timestamp: Date.now()
            });

            console.log(`[TaskManager] 任务删除成功: ${taskId} - ${task.name}`);
        } catch (error) {
            console.error(`[TaskManager] 删除任务失败 ${taskId}:`, error);
            throw error;
        }
    }

    /**
     * 更新任务
     */
    async updateTask(taskId: string, updates: TaskUpdateOptions): Promise<void> {
        const task = this.tasks.get(taskId);
        if (!task) {
            throw new Error("任务不存在");
        }

        try {
            const oldTask = { ...task };

            // 更新任务字段
            if (updates.name !== undefined) task.name = updates.name;
            if (updates.status !== undefined) task.status = updates.status;
            if (updates.position !== undefined) task.position = updates.position;
            if (updates.avgCost !== undefined) task.avgCost = updates.avgCost;
            if (updates.pnl !== undefined) task.pnl = updates.pnl;

            // 更新配置（需要重新验证）
            if (updates.strategyConfig) {
                task.strategyConfig = { ...task.strategyConfig, ...updates.strategyConfig };
                this.validateStrategyConfig(task.strategyConfig);
            }

            if (updates.riskConfig) {
                task.riskConfig = { ...task.riskConfig, ...updates.riskConfig };
            }

            task.updatedAt = new Date();

            // 如果是运行中的任务且配置有变化，需要重新订阅数据
            if (task.status === TaskStatus.Running && (updates.strategyConfig || updates.riskConfig)) {
                await this.resubscribeMarketData(task);
            }

            // 发送事件
            this.emitTaskEvent({
                type: TaskEventType.Updated,
                taskId,
                timestamp: Date.now(),
                data: { oldTask, newTask: task, updates }
            });

            console.log(`[TaskManager] 任务更新成功: ${taskId} - ${task.name}`);
        } catch (error) {
            console.error(`[TaskManager] 更新任务失败 ${taskId}:`, error);
            throw error;
        }
    }

    /**
     * 强制清仓任务
     */
    async liquidateTask(taskId: string): Promise<void> {
        const task = this.tasks.get(taskId);
        if (!task) {
            throw new Error("任务不存在");
        }

        if (task.position === 0) {
            throw new Error("该任务没有持仓");
        }

        try {
            // 使用RiskManager执行清仓
            await this.riskManager.executeLiquidation(task);

            // 停止任务
            await this.stopTask(taskId);

            // 更新状态
            task.status = TaskStatus.Liquidated;
            task.position = 0;
            task.updatedAt = new Date();

            // 发送事件
            this.emitTaskEvent({
                type: TaskEventType.Liquidated,
                taskId,
                timestamp: Date.now()
            });

            console.log(`[TaskManager] 任务清仓成功: ${taskId} - ${task.name}`);
        } catch (error) {
            task.status = TaskStatus.Error;
            console.error(`[TaskManager] 清仓任务失败 ${taskId}:`, error);
            throw error;
        }
    }

    /**
     * 获取任务
     */
    getTask(taskId: string): Task | null {
        return this.tasks.get(taskId) || null;
    }

    /**
     * 获取所有任务
     */
    getAllTasks(): Task[] {
        return Array.from(this.tasks.values());
    }

    /**
     * 获取统计信息
     */
    getStatistics(): TaskStatistics {
        const tasks = Array.from(this.tasks.values());

        return {
            totalTasks: tasks.length,
            runningTasks: tasks.filter((t) => t.status === TaskStatus.Running).length,
            pausedTasks: tasks.filter((t) => t.status === TaskStatus.Paused).length,
            stoppedTasks: tasks.filter((t) => t.status === TaskStatus.Stopped).length,
            errorTasks: tasks.filter((t) => t.status === TaskStatus.Error).length,
            liquidatedTasks: tasks.filter((t) => t.status === TaskStatus.Liquidated).length,
            totalMarketValue: tasks.reduce((sum, t) => sum + t.position * (t.avgCost || 0), 0),
            totalPnL: tasks.reduce((sum, t) => sum + t.pnl, 0),
            totalPosition: tasks.reduce((sum, t) => sum + Math.abs(t.position), 0)
        };
    }

    // 私有方法...

    /**
     * 订阅市场数据
     */
    private async subscribeMarketData(task: Task): Promise<void> {
        const requiredDataTypes = task.strategyConfig.requiredDataTypes || [DataType.Quote];

        await this.marketDataManager.subscribeForTask(task.id, task.stockCode, task.market, requiredDataTypes);
    }

    /**
     * 取消市场数据订阅
     */
    private async unsubscribeMarketData(task: Task): Promise<void> {
        await this.marketDataManager.cleanupTaskSubscriptions(task.id);
    }

    /**
     * 重新订阅市场数据
     */
    private async resubscribeMarketData(task: Task): Promise<void> {
        await this.unsubscribeMarketData(task);
        await this.subscribeMarketData(task);
    }

    /**
     * 启动任务执行
     */
    private startTaskExecution(taskId: string): void {
        // 停止现有定时器
        this.stopTaskExecution(taskId);

        // 启动新定时器
        const timer = setInterval(() => {
            this.executeTask(taskId).catch((error) => {
                console.error(`[TaskManager] 任务执行失败 ${taskId}:`, error);
                this.handleTaskError(taskId, error instanceof Error ? error : new Error(String(error)));
            });
        }, this.config.dataUpdateInterval);

        this.taskTimers.set(taskId, timer);
    }

    /**
     * 停止任务执行
     */
    private stopTaskExecution(taskId: string): void {
        const timer = this.taskTimers.get(taskId);
        if (timer) {
            clearInterval(timer);
            this.taskTimers.delete(taskId);
        }
    }

    /**
     * 执行任务
     */
    private async executeTask(taskId: string): Promise<void> {
        const context = this.executionContexts.get(taskId);
        if (!context || !context.task.isActive) {
            return;
        }

        try {
            // 获取最新市场数据
            const marketData = this.marketDataManager.getRealtimeDataForTask(taskId, context.task.stockCode, context.task.market);

            if (!marketData) {
                return; // 暂无数据
            }

            // 更新执行上下文
            context.marketData = marketData;
            context.currentPrice = this.extractCurrentPrice(marketData);
            context.lastUpdateTime = Date.now();

            // 执行策略
            const strategyResult = await this.executeStrategy(context);

            // 检查风险条件
            const riskResult = await this.checkRiskConditions(context);

            // 处理策略信号
            if (strategyResult.signal) {
                await this.handleStrategySignal(context, strategyResult.signal);
            }

            // 处理风险触发
            if (riskResult.shouldLiquidate) {
                await this.handleRiskTrigger(context, riskResult);
            }
        } catch (error) {
            console.error(`[TaskManager] 执行任务失败 ${taskId}:`, error);
            this.handleTaskError(taskId, error instanceof Error ? error : new Error(String(error)));
        }
    }

    /**
     * 处理市场数据
     */
    private handleMarketData(eventData: any): void {
        const { taskIds, stockCode, market, data } = eventData;

        // 更新相关任务的执行上下文
        for (const taskId of taskIds) {
            const context = this.executionContexts.get(taskId);
            if (context && context.task.stockCode === stockCode && context.task.market === market) {
                context.marketData = data;
                context.currentPrice = this.extractCurrentPrice(data);
                context.lastUpdateTime = Date.now();
            }
        }
    }

    /**
     * 处理市场数据错误
     */
    private handleMarketDataError(error: Error): void {
        console.error("[TaskManager] 市场数据错误:", error);
        // 暂停所有运行中的任务
        for (const [taskId, task] of this.tasks.entries()) {
            if (task.status === TaskStatus.Running) {
                this.pauseTask(taskId).catch(console.error);
            }
        }
    }

    /**
     * 生成任务ID
     */
    private generateTaskId(): string {
        return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取策略显示名称
     */
    private getStrategyDisplayName(config: StrategyConfig): string {
        const displayNames = {
            strategy_a_big_order_monitor: "大单监控策略",
            strategy_b_breakout_chase: "突破追涨策略",
            strategy_e_mean_reversion: "均值回归策略",
            strategy_d_momentum: "动量追踪策略"
        };

        return displayNames[config.strategyType as keyof typeof displayNames] || "未知策略";
    }

    /**
     * 验证策略配置
     */
    private validateStrategyConfig(config: StrategyConfig): void {
        if (!config.strategyType) {
            throw new Error("策略类型不能为空");
        }

        if (!config.params) {
            throw new Error("策略参数不能为空");
        }

        // 使用策略引擎验证配置
        if (!this.strategyEngine.validateStrategyConfig(config)) {
            throw new Error("策略配置验证失败");
        }
    }

    /**
     * 发送任务事件
     */
    private emitTaskEvent(event: TaskEvent): void {
        this.emit("taskEvent", event);
        this.emit(event.type, event);
    }

    /**
     * 处理任务错误
     */
    private handleTaskError(taskId: string, error: Error): void {
        const task = this.tasks.get(taskId);
        if (task) {
            task.status = TaskStatus.Error;
            task.updatedAt = new Date();

            this.emitTaskEvent({
                type: TaskEventType.Error,
                taskId,
                timestamp: Date.now(),
                data: { error: error.message }
            });
        }
    }

    /**
     * 清理任务资源
     */
    private cleanupTask(taskId: string): void {
        this.stopTaskExecution(taskId);
        this.executionContexts.delete(taskId);
    }

    /**
     * 启动定时任务
     */
    private startPeriodicTasks(): void {
        // 定期风险检查
        setInterval(() => {
            this.performPeriodicRiskCheck().catch(console.error);
        }, this.config.riskCheckInterval);

        // 定期状态更新
        setInterval(() => {
            this.updateTaskStatistics();
        }, 30000); // 30秒更新一次统计
    }

    /**
     * 定期风险检查
     */
    private async performPeriodicRiskCheck(): Promise<void> {
        for (const [taskId, task] of this.tasks.entries()) {
            if (task.status === TaskStatus.Running) {
                const context = this.executionContexts.get(taskId);
                if (context) {
                    try {
                        const riskResult = await this.checkRiskConditions(context);
                        if (riskResult.shouldLiquidate) {
                            await this.handleRiskTrigger(context, riskResult);
                        }
                    } catch (error) {
                        console.error(`[TaskManager] 风险检查失败 ${taskId}:`, error);
                    }
                }
            }
        }
    }

    /**
     * 更新任务统计
     */
    private updateTaskStatistics(): void {
        const stats = this.getStatistics();
        this.emit("statisticsUpdated", stats);
    }

    /**
     * 提取当前价格
     */
    private extractCurrentPrice(marketData: RealtimeData): number {
        if (marketData.type === DataType.Quote && marketData.data) {
            return (marketData.data as any).price || 0;
        }
        return 0;
    }

    /**
     * 执行策略
     */
    private async executeStrategy(context: TaskExecutionContext): Promise<StrategyExecutionResult> {
        try {
            // 获取策略实例
            const strategy = this.strategyEngine.getStrategy(context.task.strategyConfig.strategyType);

            if (!strategy) {
                throw new Error(`未找到策略类型: ${context.task.strategyConfig.strategyType}`);
            }

            // 执行策略
            const result = await strategy.execute(context);

            // 记录策略执行
            console.log(`[TaskManager] 策略执行完成 ${context.task.id}: ${strategy.name}`);

            return result;
        } catch (error) {
            console.error(`[TaskManager] 策略执行失败 ${context.task.id}:`, error);
            return {
                shouldContinue: true,
                error: error instanceof Error ? error : new Error(String(error))
            };
        }
    }

    /**
     * 检查风险条件
     */
    private async checkRiskConditions(context: TaskExecutionContext): Promise<RiskCheckResult> {
        return await this.riskManager.checkRiskConditions(context);
    }

    /**
     * 处理风险触发
     */
    private async handleRiskTrigger(context: TaskExecutionContext, riskResult: RiskCheckResult): Promise<void> {
        try {
            const { task } = context;

            console.log(`[TaskManager] 风险触发 ${task.id}: 清仓 - 条件: ${riskResult.triggeredConditions.join(", ")}`);

            // 发送风险触发事件
            this.emitTaskEvent({
                type: TaskEventType.Updated,
                taskId: task.id,
                timestamp: Date.now(),
                data: {
                    riskTriggered: true,
                    triggeredConditions: riskResult.triggeredConditions,
                    urgency: riskResult.urgency
                }
            });

            // 使用RiskManager执行清仓
            await this.riskManager.executeLiquidation(task, riskResult.liquidationStrategy);

            // 更新任务状态为已清仓
            task.status = TaskStatus.Liquidated;
            task.updatedAt = new Date();
        } catch (error) {
            console.error(`[TaskManager] 处理风险触发失败 ${context.task.id}:`, error);
            this.handleTaskError(context.task.id, error instanceof Error ? error : new Error(String(error)));
        }
    }

    /**
     * 处理策略信号
     */
    private async handleStrategySignal(context: TaskExecutionContext, signal: StrategySignal): Promise<void> {
        try {
            const { task } = context;

            console.log(`[TaskManager] 处理策略信号 ${task.id}: ${signal.type} - ${signal.reason}`);

            // 发送信号事件
            this.emitTaskEvent({
                type: TaskEventType.Updated,
                taskId: task.id,
                timestamp: Date.now(),
                data: {
                    signalType: signal.type,
                    confidence: signal.confidence,
                    reason: signal.reason,
                    price: signal.price,
                    quantity: signal.quantity
                }
            });

            // 根据信号类型执行相应操作
            switch (signal.type) {
                case "buy":
                    await this.executeBuySignal(context, signal);
                    break;
                case "sell":
                    await this.executeSellSignal(context, signal);
                    break;
                case "hold":
                    // 持有信号，不执行交易
                    console.log(`[TaskManager] 持有信号 ${task.id}: ${signal.reason}`);
                    break;
                default:
                    console.warn(`[TaskManager] 未知信号类型: ${signal.type}`);
            }
        } catch (error) {
            console.error(`[TaskManager] 处理策略信号失败 ${context.task.id}:`, error);
            this.handleTaskError(context.task.id, error instanceof Error ? error : new Error(String(error)));
        }
    }

    /**
     * 执行买入信号
     */
    private async executeBuySignal(context: TaskExecutionContext, signal: StrategySignal): Promise<void> {
        const { task } = context;

        // 检查是否已有仓位
        if (task.position > 0) {
            console.log(`[TaskManager] 任务 ${task.id} 已有仓位，跳过买入信号`);
            return;
        }

        // 模拟买入执行（实际应该调用TradingManager）
        const quantity = signal.quantity || 100;
        const price = signal.price || context.currentPrice;

        console.log(`[TaskManager] 执行买入 ${task.id}: ${quantity}股 @ ${price}`);

        // 更新任务仓位信息
        task.position = quantity;
        task.avgCost = price;
        task.updatedAt = new Date();

        // 发送仓位变化事件
        this.emitTaskEvent({
            type: TaskEventType.PositionChanged,
            taskId: task.id,
            timestamp: Date.now(),
            data: {
                oldPosition: 0,
                newPosition: quantity,
                avgCost: price,
                signal: signal
            }
        });
    }

    /**
     * 执行卖出信号
     */
    private async executeSellSignal(context: TaskExecutionContext, signal: StrategySignal): Promise<void> {
        const { task } = context;

        // 检查是否有仓位可卖
        if (task.position <= 0) {
            console.log(`[TaskManager] 任务 ${task.id} 无仓位，跳过卖出信号`);
            return;
        }

        // 模拟卖出执行
        const quantity = signal.quantity || task.position;
        const sellQuantity = Math.min(quantity, task.position);
        const price = signal.price || context.currentPrice;

        console.log(`[TaskManager] 执行卖出 ${task.id}: ${sellQuantity}股 @ ${price}`);

        // 计算实现盈亏
        const realizedPnL = (price - (task.avgCost || 0)) * sellQuantity;

        // 更新任务仓位信息
        const oldPosition = task.position;
        task.position -= sellQuantity;
        task.pnl += realizedPnL;
        task.updatedAt = new Date();

        // 如果完全平仓，清除成本价
        if (task.position === 0) {
            task.avgCost = undefined;
        }

        // 发送仓位变化事件
        this.emitTaskEvent({
            type: TaskEventType.PositionChanged,
            taskId: task.id,
            timestamp: Date.now(),
            data: {
                oldPosition,
                newPosition: task.position,
                realizedPnL,
                signal: signal
            }
        });
    }

    /**
     * 关闭任务管理器
     */
    async shutdown(): Promise<void> {
        console.log("[TaskManager] 正在关闭...");

        // 停止所有任务
        for (const taskId of this.tasks.keys()) {
            try {
                await this.stopTask(taskId);
            } catch (error) {
                console.error(`[TaskManager] 停止任务失败 ${taskId}:`, error);
            }
        }

        // 清理定时器
        for (const timer of this.taskTimers.values()) {
            clearInterval(timer);
        }
        this.taskTimers.clear();

        // 清理资源
        this.tasks.clear();
        this.executionContexts.clear();

        this.isInitialized = false;
        console.log("[TaskManager] 已关闭");
    }

    /**
     * 获取任务总数
     */
    getTaskCount(): number {
        return this.tasks.size;
    }

    /**
     * 获取活跃任务数量
     */
    getActiveTaskCount(): number {
        let count = 0;
        for (const task of this.tasks.values()) {
            if (task.status === TaskStatus.Running || task.status === TaskStatus.Paused) {
                count++;
            }
        }
        return count;
    }

    /**
     * 设置市场数据连接
     */
    private setupMarketDataConnection(): void {
        if (this.marketDataManager.isAdapterConnected()) {
            console.log("[TaskManager] MarketDataManager 已连接，启用行情数据功能");
            // 设置数据事件监听
            this.marketDataManager.removeAllListeners("taskData");
            this.marketDataManager.removeAllListeners("error");
            this.marketDataManager.on("taskData", this.handleMarketData.bind(this));
            this.marketDataManager.on("error", this.handleMarketDataError.bind(this));
        } else {
            console.log("[TaskManager] MarketDataManager 未连接，将在无行情数据模式下运行");
            // 在无行情数据模式下，某些功能可能受限，但系统仍可正常运行
        }
    }

    /**
     * 检查服务可用性
     */
    isMarketDataAvailable(): boolean {
        return this.marketDataManager && this.marketDataManager.isAdapterConnected();
    }

    isTradingAvailable(): boolean {
        return this.tradingManager && this.tradingManager.getAllAdapters().size > 0;
    }
}
