/**
 * Electron 任务管理系统
 * ====================
 * 统一入口类，整合所有任务管理相关组件
 * 为前端和主进程提供完整的任务管理接口
 */

import { EventEmitter } from "events";
import { TaskManager } from "./TaskManager";
import { StrategyEngine } from "./StrategyEngine";
import { RiskManager } from "./RiskManager";
import { StrategyFactory } from "./StrategyFactory";
import { MarketDataManager } from "./MarketDataManager";
import { TradingManager } from "./TradingManager";
import { getConfig, validateConfig } from "../utils/configManager";
import { Task, TaskStatus, TaskEvent, TaskCreateOptions, TaskUpdateOptions, TaskStatistics, StrategyConfig, RiskConfig, TaskManagerConfig } from "./task-types";
import { Market, DataType } from "./market-types";
import { ServiceManager } from "../services/ServiceManager";

export interface TradingSystemConfig {
    taskManager?: Partial<TaskManagerConfig>;
    enablePersistence?: boolean;
    logLevel?: "debug" | "info" | "warn" | "error";
}

export class TradingSystem extends EventEmitter {
    private taskManager: TaskManager;
    private strategyEngine: StrategyEngine;
    private riskManager: RiskManager;
    private strategyFactory: StrategyFactory;
    private marketDataManager: MarketDataManager;
    private tradingManager: TradingManager;
    private serviceManager?: ServiceManager;

    private isInitialized: boolean = false;
    private config: TradingSystemConfig;
    private startTime: number = Date.now();

    constructor(marketDataManager?: MarketDataManager, tradingManager?: TradingManager, config?: TradingSystemConfig) {
        super();

        this.config = {
            enablePersistence: true,
            logLevel: "info",
            ...config
        };

        // 如果没有传入实例，则创建新的实例
        if (!marketDataManager) {
            this.marketDataManager = new MarketDataManager();
        } else {
            this.marketDataManager = marketDataManager;
        }

        if (!tradingManager) {
            const globalConfig = getConfig();
            this.tradingManager = new TradingManager(globalConfig.trading);
        } else {
            this.tradingManager = tradingManager;
        }

        // 初始化核心组件
        this.strategyEngine = new StrategyEngine();
        this.riskManager = new RiskManager();
        this.strategyFactory = new StrategyFactory();

        // 初始化任务管理器
        this.taskManager = new TaskManager(this.marketDataManager, this.tradingManager, this.strategyEngine, this.riskManager, this.config.taskManager);

        this.setupEventHandlers();
    }

    /**
     * 创建配置好的TradingSystem实例
     */
    static async createWithConfig(environment?: "production" | "test" | "development"): Promise<TradingSystem> {
        // 设置环境变量
        if (environment) {
            process.env.NODE_ENV = environment;
        }

        const config = getConfig();

        // 验证配置
        if (!validateConfig(config)) {
            throw new Error("配置验证失败");
        }

        console.log(`[TradingSystem] 使用 ${process.env.NODE_ENV || "development"} 环境配置`);

        // 创建服务管理器
        const serviceManager = new ServiceManager({
            enableMarketData: config.market.enabled,
            enableTrading: config.trading.huasheng.enabled,
            gracefulShutdown: true
        });

        // 初始化所有服务（并行且独立）
        try {
            await serviceManager.initialize(config);
            console.log("[TradingSystem] 服务管理器初始化完成");
        } catch (error) {
            console.error("[TradingSystem] 服务管理器初始化失败:", error);
            // 即使部分服务失败，系统仍继续运行
        }

        // 获取服务实例（可能为null）
        const marketDataManager = serviceManager.getMarketDataManager() || new MarketDataManager();
        const tradingManager = serviceManager.getTradingManager() || new TradingManager(config.trading);

        // 创建TradingSystem实例
        const tradingSystem = new TradingSystem(marketDataManager, tradingManager, config.system);
        tradingSystem.serviceManager = serviceManager;

        // 监听服务状态变化
        serviceManager.on("service-connected", (serviceName) => {
            console.log(`[TradingSystem] ${serviceName} 服务已连接`);
            tradingSystem.emit(`${serviceName}-connected`);
        });

        serviceManager.on("service-disconnected", (serviceName) => {
            console.log(`[TradingSystem] ${serviceName} 服务连接断开`);
            tradingSystem.emit(`${serviceName}-disconnected`);
        });

        serviceManager.on("service-error", (serviceName, error) => {
            console.error(`[TradingSystem] ${serviceName} 服务错误:`, error);
            tradingSystem.emit(`${serviceName}-error`, error);
        });

        return tradingSystem;
    }

    /**
     * 初始化交易系统
     */
    async initialize(): Promise<void> {
        try {
            console.log("[TradingSystem] 正在初始化交易系统...");

            // 先初始化核心组件（不依赖外部连接）
            await this.taskManager.initialize();
            this.setupEventForwarding();

            // 标记系统已初始化（即使交易适配器未连接）
            this.isInitialized = true;
            this.emit("initialized");
            console.log("[TradingSystem] 交易系统核心初始化完成");

            // 异步尝试连接交易适配器（不阻塞系统初始化）
            this.initializeTradingAdaptersAsync();
        } catch (error) {
            console.error("[TradingSystem] 初始化失败:", error);
            throw error;
        }
    }

    /**
     * 异步初始化交易适配器（不阻塞系统启动）
     */
    private async initializeTradingAdaptersAsync(): Promise<void> {
        // 在后台异步连接华盛通适配器
        setTimeout(async () => {
            try {
                const huashengAdapter = this.tradingManager.getAdapter("huasheng");
                if (huashengAdapter) {
                    console.log("[TradingSystem] 后台连接华盛通交易适配器...");
                    await huashengAdapter.connect();
                    console.log("[TradingSystem] 华盛通交易适配器连接成功");
                    this.emit("trading-adapter-connected", "huasheng");
                } else {
                    console.log("[TradingSystem] 华盛通适配器未启用或未初始化");
                }
            } catch (error) {
                console.error("[TradingSystem] 华盛通交易适配器连接失败:", error);
                this.emit("trading-adapter-error", "huasheng", error);
                // 不影响系统运行，只是交易功能不可用
            }
        }, 100); // 延迟100ms执行，确保系统初始化完成
    }

    /**
     * 设置事件处理器
     */
    private setupEventHandlers(): void {
        // 监听进程退出
        process.on("SIGINT", () => this.shutdown());
        process.on("SIGTERM", () => this.shutdown());
    }

    /**
     * 设置事件转发
     */
    private setupEventForwarding(): void {
        // 转发任务管理器事件
        this.taskManager.on("taskEvent", (event: TaskEvent) => {
            this.emit("taskEvent", event);
        });

        this.taskManager.on("statisticsUpdated", (stats: TaskStatistics) => {
            this.emit("statisticsUpdated", stats);
        });

        // 转发策略工厂事件
        this.strategyFactory.on("templateRegistered", (template) => {
            this.emit("strategyTemplateRegistered", template);
        });
    }

    /**
     * 获取系统状态
     */
    getStatus() {
        const tradingAdapters = this.tradingManager.getAllAdapters();
        const tradingStatus: Record<string, any> = {};

        // 获取所有交易适配器状态
        for (const [name, adapter] of tradingAdapters) {
            if (adapter && typeof adapter.getStatus === "function") {
                tradingStatus[name] = adapter.getStatus();
            } else {
                tradingStatus[name] = { connected: false, error: "适配器未初始化" };
            }
        }

        // 获取服务管理器状态
        const servicesStatus = this.serviceManager ? this.serviceManager.getServicesStatus() : {};

        return {
            isInitialized: this.isInitialized,
            marketDataConnected: this.marketDataManager.isAdapterConnected(),
            tradingAdapters: tradingStatus,
            services: servicesStatus,
            taskCount: this.isInitialized ? this.taskManager.getTaskCount() : 0,
            activeTaskCount: this.isInitialized ? this.taskManager.getActiveTaskCount() : 0,
            systemUptime: Date.now() - this.startTime
        };
    }

    // ==================== 任务管理接口 ====================

    /**
     * 创建新任务
     */
    async createTask(options: TaskCreateOptions): Promise<string> {
        this.ensureInitialized();
        return await this.taskManager.createTask(options);
    }

    /**
     * 启动任务
     */
    async startTask(taskId: string): Promise<void> {
        this.ensureInitialized();
        return await this.taskManager.startTask(taskId);
    }

    /**
     * 停止任务
     */
    async stopTask(taskId: string): Promise<void> {
        this.ensureInitialized();
        return await this.taskManager.stopTask(taskId);
    }

    /**
     * 暂停任务
     */
    async pauseTask(taskId: string): Promise<void> {
        this.ensureInitialized();
        return await this.taskManager.pauseTask(taskId);
    }

    /**
     * 恢复任务
     */
    async resumeTask(taskId: string): Promise<void> {
        this.ensureInitialized();
        return await this.taskManager.resumeTask(taskId);
    }

    /**
     * 删除任务
     */
    async deleteTask(taskId: string): Promise<void> {
        this.ensureInitialized();
        return await this.taskManager.deleteTask(taskId);
    }

    /**
     * 更新任务
     */
    async updateTask(taskId: string, updates: TaskUpdateOptions): Promise<void> {
        this.ensureInitialized();
        return await this.taskManager.updateTask(taskId, updates);
    }

    /**
     * 强制清仓任务
     */
    async liquidateTask(taskId: string): Promise<void> {
        this.ensureInitialized();
        return await this.taskManager.liquidateTask(taskId);
    }

    /**
     * 启动所有任务
     */
    async startAllTasks(): Promise<void> {
        this.ensureInitialized();
        const tasks = this.taskManager.getAllTasks();
        const stoppedTasks = tasks.filter((t) => t.status === TaskStatus.Stopped || t.status === TaskStatus.Paused);

        for (const task of stoppedTasks) {
            try {
                await this.taskManager.startTask(task.id);
            } catch (error) {
                console.error(`[TradingSystem] 启动任务失败 ${task.id}:`, error);
            }
        }
    }

    /**
     * 停止所有任务
     */
    async stopAllTasks(): Promise<void> {
        this.ensureInitialized();
        const tasks = this.taskManager.getAllTasks();
        const runningTasks = tasks.filter((t) => t.status === TaskStatus.Running);

        for (const task of runningTasks) {
            try {
                await this.taskManager.stopTask(task.id);
            } catch (error) {
                console.error(`[TradingSystem] 停止任务失败 ${task.id}:`, error);
            }
        }
    }

    /**
     * 获取任务
     */
    getTask(taskId: string): Task | null {
        this.ensureInitialized();
        return this.taskManager.getTask(taskId);
    }

    /**
     * 获取所有任务
     */
    getAllTasks(): Task[] {
        this.ensureInitialized();
        return this.taskManager.getAllTasks();
    }

    /**
     * 获取统计信息
     */
    getStatistics(): TaskStatistics {
        this.ensureInitialized();
        return this.taskManager.getStatistics();
    }

    // ==================== 策略管理接口 ====================

    /**
     * 获取所有策略模板
     */
    getStrategyTemplates() {
        return this.strategyFactory.getAllTemplates();
    }

    /**
     * 获取策略模板
     */
    getStrategyTemplate(strategyType: string) {
        return this.strategyFactory.getTemplate(strategyType as any);
    }

    /**
     * 按分类获取策略模板
     */
    getStrategyTemplatesByCategory(category: string) {
        return this.strategyFactory.getTemplatesByCategory(category as any);
    }

    /**
     * 创建策略配置
     */
    createStrategyConfig(strategyType: string, params: Record<string, any>): StrategyConfig {
        return this.strategyFactory.createStrategyConfig(strategyType as any, params);
    }

    /**
     * 验证策略配置
     */
    validateStrategyConfig(config: StrategyConfig): boolean {
        return this.strategyFactory.validateStrategyConfig(config);
    }

    /**
     * 获取策略配置摘要
     */
    getStrategyConfigSummary(config: StrategyConfig): string {
        return this.strategyFactory.getConfigSummary(config);
    }

    /**
     * 搜索策略模板
     */
    searchStrategyTemplates(query: string) {
        return this.strategyFactory.searchTemplates(query);
    }

    /**
     * 获取策略统计信息
     */
    getStrategyStatistics() {
        return this.strategyFactory.getStatistics();
    }

    // ==================== 系统状态接口 ====================

    /**
     * 获取系统状态
     */
    getSystemStatus() {
        return {
            isInitialized: this.isInitialized,
            marketDataConnected: this.marketDataManager.isAdapterConnected(),
            tradingConnected: this.tradingManager ? true : false, // 需要实现TradingManager的连接状态检查
            totalTasks: this.isInitialized ? this.taskManager.getAllTasks().length : 0,
            runningTasks: this.isInitialized ? this.taskManager.getAllTasks().filter((t) => t.status === TaskStatus.Running).length : 0
        };
    }

    /**
     * 获取行情数据统计信息
     */
    getMarketDataStats() {
        return this.marketDataManager.getStatistics();
    }

    /**
     * 获取交易状态
     */
    getTradingStatus() {
        return this.tradingManager.getAllStatus();
    }

    /**
     * 获取系统配置
     */
    getSystemConfig() {
        return {
            ...this.config,
            taskManagerConfig: this.isInitialized ? this.taskManager["config"] : undefined
        };
    }

    /**
     * 健康检查
     */
    async healthCheck(): Promise<{
        healthy: boolean;
        checks: Array<{
            component: string;
            status: "ok" | "error" | "warning";
            message?: string;
        }>;
    }> {
        const checks = [];
        let healthy = true;

        // 检查初始化状态
        if (!this.isInitialized) {
            checks.push({
                component: "system",
                status: "error" as const,
                message: "系统未初始化"
            });
            healthy = false;
        } else {
            checks.push({
                component: "system",
                status: "ok" as const
            });
        }

        // 检查市场数据连接
        if (this.marketDataManager.isAdapterConnected()) {
            checks.push({
                component: "market_data",
                status: "ok" as const
            });
        } else {
            checks.push({
                component: "market_data",
                status: "error" as const,
                message: "市场数据连接断开"
            });
            healthy = false;
        }

        // 检查任务状态
        if (this.isInitialized) {
            const stats = this.taskManager.getStatistics();
            if (stats.errorTasks > 0) {
                checks.push({
                    component: "tasks",
                    status: "warning" as const,
                    message: `${stats.errorTasks} 个任务处于错误状态`
                });
            } else {
                checks.push({
                    component: "tasks",
                    status: "ok" as const
                });
            }
        }

        return { healthy, checks };
    }

    // ==================== 高级功能接口 ====================

    /**
     * 批量操作任务
     */
    async batchOperateTasks(
        taskIds: string[],
        operation: "start" | "stop" | "pause" | "resume" | "delete"
    ): Promise<{
        succeeded: string[];
        failed: Array<{ taskId: string; error: string }>;
    }> {
        this.ensureInitialized();

        const succeeded: string[] = [];
        const failed: Array<{ taskId: string; error: string }> = [];

        for (const taskId of taskIds) {
            try {
                switch (operation) {
                    case "start":
                        await this.taskManager.startTask(taskId);
                        break;
                    case "stop":
                        await this.taskManager.stopTask(taskId);
                        break;
                    case "pause":
                        await this.taskManager.pauseTask(taskId);
                        break;
                    case "resume":
                        await this.taskManager.resumeTask(taskId);
                        break;
                    case "delete":
                        await this.taskManager.deleteTask(taskId);
                        break;
                }
                succeeded.push(taskId);
            } catch (error) {
                failed.push({
                    taskId,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }

        return { succeeded, failed };
    }

    /**
     * 导出任务配置
     */
    exportTaskConfig(taskId: string): any {
        this.ensureInitialized();
        const task = this.taskManager.getTask(taskId);
        if (!task) {
            throw new Error("任务不存在");
        }

        return {
            name: task.name,
            stockCode: task.stockCode,
            stockName: task.stockName,
            market: task.market,
            strategyConfig: task.strategyConfig,
            riskConfig: task.riskConfig,
            exportedAt: new Date().toISOString(),
            version: "1.0"
        };
    }

    /**
     * 导入任务配置
     */
    async importTaskConfig(configData: any, autoStart: boolean = false): Promise<string> {
        this.ensureInitialized();

        // 验证配置数据
        if (!configData.name || !configData.stockCode || !configData.strategyConfig) {
            throw new Error("配置数据不完整");
        }

        // 创建任务
        const taskId = await this.taskManager.createTask({
            name: configData.name,
            stockCode: configData.stockCode,
            stockName: configData.stockName,
            market: configData.market,
            strategyConfig: configData.strategyConfig,
            riskConfig: configData.riskConfig,
            autoStart
        });

        return taskId;
    }

    // ==================== 内部方法 ====================

    /**
     * 确保系统已初始化
     */
    private ensureInitialized(): void {
        if (!this.isInitialized) {
            throw new Error("交易系统未初始化，请先调用 initialize() 方法");
        }
    }

    /**
     * 关闭交易系统
     */
    async shutdown(): Promise<void> {
        console.log("[TradingSystem] 正在关闭交易系统...");

        try {
            if (this.isInitialized) {
                await this.taskManager.shutdown();
            }

            this.isInitialized = false;
            this.emit("shutdown");

            console.log("[TradingSystem] 交易系统已关闭");
        } catch (error) {
            console.error("[TradingSystem] 关闭失败:", error);
            throw error;
        }
    }
}
