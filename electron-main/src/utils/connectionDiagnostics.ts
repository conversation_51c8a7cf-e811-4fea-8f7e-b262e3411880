/**
 * 连接诊断工具
 * ================
 * 用于诊断行情接口和交易接口的连接问题
 */

import { getConfig } from './configManager';
import net from 'net';

export interface DiagnosticResult {
    service: string;
    status: 'success' | 'error' | 'warning';
    message: string;
    details?: any;
}

export class ConnectionDiagnostics {
    /**
     * 运行完整的连接诊断
     */
    static async runFullDiagnostics(): Promise<DiagnosticResult[]> {
        const results: DiagnosticResult[] = [];
        
        try {
            const config = getConfig();
            
            // 检查配置文件
            results.push(await this.checkConfiguration(config));
            
            // 检查行情服务连接
            if (config.market.enabled) {
                results.push(await this.checkMarketDataConnection(config.market));
            } else {
                results.push({
                    service: '行情数据服务',
                    status: 'warning',
                    message: '行情数据服务已禁用'
                });
            }
            
            // 检查交易服务连接
            if (config.trading.huasheng.enabled) {
                results.push(await this.checkTradingConnection(config.trading.huasheng));
            } else {
                results.push({
                    service: '华盛通交易服务',
                    status: 'warning',
                    message: '华盛通交易服务已禁用'
                });
            }
            
        } catch (error) {
            results.push({
                service: '系统配置',
                status: 'error',
                message: `配置加载失败: ${error instanceof Error ? error.message : '未知错误'}`
            });
        }
        
        return results;
    }

    /**
     * 检查配置文件
     */
    private static async checkConfiguration(config: any): Promise<DiagnosticResult> {
        try {
            // 检查必要的配置项
            const requiredFields = ['app', 'market', 'trading', 'system'];
            const missingFields = requiredFields.filter(field => !config[field]);
            
            if (missingFields.length > 0) {
                return {
                    service: '系统配置',
                    status: 'error',
                    message: `缺少必要配置项: ${missingFields.join(', ')}`
                };
            }
            
            return {
                service: '系统配置',
                status: 'success',
                message: '配置文件验证通过',
                details: {
                    environment: config.app.environment,
                    marketEnabled: config.market.enabled,
                    tradingEnabled: config.trading.huasheng.enabled
                }
            };
        } catch (error) {
            return {
                service: '系统配置',
                status: 'error',
                message: `配置验证失败: ${error instanceof Error ? error.message : '未知错误'}`
            };
        }
    }

    /**
     * 检查行情数据连接
     */
    private static async checkMarketDataConnection(marketConfig: any): Promise<DiagnosticResult> {
        try {
            const isReachable = await this.checkTcpConnection(marketConfig.host, marketConfig.port, 5000);
            
            if (isReachable) {
                return {
                    service: '富途行情服务',
                    status: 'success',
                    message: `成功连接到 ${marketConfig.host}:${marketConfig.port}`,
                    details: {
                        host: marketConfig.host,
                        port: marketConfig.port,
                        provider: marketConfig.provider
                    }
                };
            } else {
                return {
                    service: '富途行情服务',
                    status: 'error',
                    message: `无法连接到 ${marketConfig.host}:${marketConfig.port}`,
                    details: {
                        host: marketConfig.host,
                        port: marketConfig.port,
                        suggestion: '请确保富途OpenD已启动并监听指定端口'
                    }
                };
            }
        } catch (error) {
            return {
                service: '富途行情服务',
                status: 'error',
                message: `连接检查失败: ${error instanceof Error ? error.message : '未知错误'}`
            };
        }
    }

    /**
     * 检查交易服务连接
     */
    private static async checkTradingConnection(tradingConfig: any): Promise<DiagnosticResult> {
        try {
            const isReachable = await this.checkTcpConnection(tradingConfig.host, tradingConfig.port, 5000);
            
            if (isReachable) {
                return {
                    service: '华盛通交易服务',
                    status: 'success',
                    message: `成功连接到 ${tradingConfig.host}:${tradingConfig.port}`,
                    details: {
                        host: tradingConfig.host,
                        port: tradingConfig.port,
                        account: tradingConfig.account
                    }
                };
            } else {
                return {
                    service: '华盛通交易服务',
                    status: 'error',
                    message: `无法连接到 ${tradingConfig.host}:${tradingConfig.port}`,
                    details: {
                        host: tradingConfig.host,
                        port: tradingConfig.port,
                        suggestion: '请确保华盛通交易服务器可访问'
                    }
                };
            }
        } catch (error) {
            return {
                service: '华盛通交易服务',
                status: 'error',
                message: `连接检查失败: ${error instanceof Error ? error.message : '未知错误'}`
            };
        }
    }

    /**
     * 检查TCP连接
     */
    private static checkTcpConnection(host: string, port: number, timeout: number = 5000): Promise<boolean> {
        return new Promise((resolve) => {
            const socket = new net.Socket();
            
            const timer = setTimeout(() => {
                socket.destroy();
                resolve(false);
            }, timeout);
            
            socket.on('connect', () => {
                clearTimeout(timer);
                socket.destroy();
                resolve(true);
            });
            
            socket.on('error', () => {
                clearTimeout(timer);
                resolve(false);
            });
            
            socket.connect(port, host);
        });
    }

    /**
     * 生成诊断报告
     */
    static generateReport(results: DiagnosticResult[]): string {
        let report = '\n=== 连接诊断报告 ===\n\n';
        
        results.forEach((result, index) => {
            const statusIcon = result.status === 'success' ? '✅' : 
                             result.status === 'warning' ? '⚠️' : '❌';
            
            report += `${index + 1}. ${statusIcon} ${result.service}\n`;
            report += `   状态: ${result.message}\n`;
            
            if (result.details) {
                report += `   详情: ${JSON.stringify(result.details, null, 2)}\n`;
            }
            
            report += '\n';
        });
        
        // 添加建议
        const errors = results.filter(r => r.status === 'error');
        if (errors.length > 0) {
            report += '=== 问题解决建议 ===\n\n';
            
            errors.forEach((error, index) => {
                report += `${index + 1}. ${error.service}:\n`;
                report += `   ${error.message}\n`;
                
                if (error.details?.suggestion) {
                    report += `   建议: ${error.details.suggestion}\n`;
                }
                
                report += '\n';
            });
        }
        
        return report;
    }
}
