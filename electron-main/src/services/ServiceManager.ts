/**
 * 服务管理器
 * ==========
 * 统一管理所有独立服务的生命周期
 * 确保各服务之间的独立性和容错性
 */

import { EventEmitter } from "events";
import { MarketDataManager } from "../trading/MarketDataManager";
import { TradingManager } from "../trading/TradingManager";
import { AppConfig } from "../utils/configManager";
import { WindowManager } from "../windowManager";

export interface ServiceStatus {
    name: string;
    status: "initializing" | "running" | "error" | "stopped" | "disabled";
    connected: boolean;
    enabled: boolean; // 新增：服务是否启用
    error?: string;
    lastUpdate: number;
}

export interface ServiceManagerConfig {
    enableMarketData: boolean;
    enableTrading: boolean;
    gracefulShutdown: boolean;
}

export class ServiceManager extends EventEmitter {
    private marketDataManager: MarketDataManager | null = null;
    private tradingManager: TradingManager | null = null;
    private config: ServiceManagerConfig;
    private services: Map<string, ServiceStatus> = new Map();
    private isInitialized = false;

    constructor(
        config: ServiceManagerConfig = {
            enableMarketData: true,
            enableTrading: true,
            gracefulShutdown: true
        }
    ) {
        super();
        this.config = config;
        this.initializeServiceStatus();
    }

    /**
     * 初始化服务状态
     */
    private initializeServiceStatus(): void {
        // 始终创建行情服务状态条目
        this.services.set("marketData", {
            name: "行情数据服务",
            status: this.config.enableMarketData ? "stopped" : "disabled",
            connected: false,
            enabled: this.config.enableMarketData,
            lastUpdate: Date.now()
        });

        // 始终创建交易服务状态条目
        this.services.set("trading", {
            name: "交易服务",
            status: this.config.enableTrading ? "stopped" : "disabled",
            connected: false,
            enabled: this.config.enableTrading,
            lastUpdate: Date.now()
        });
    }

    /**
     * 启动所有服务
     */
    async initialize(appConfig: AppConfig): Promise<void> {
        console.log("[ServiceManager] 开始初始化服务...");

        const initPromises: Promise<void>[] = [];

        // 更新服务配置状态
        this.updateServiceConfigStatus(appConfig);

        // 并行初始化启用的服务
        if (this.config.enableMarketData && appConfig.market.enabled) {
            initPromises.push(this.initializeMarketDataService(appConfig.market));
        } else if (!appConfig.market.enabled) {
            console.log("[ServiceManager] 行情服务已在配置中禁用");
            this.updateServiceStatus("marketData", "disabled", false, "配置中已禁用");
        }

        if (this.config.enableTrading && appConfig.trading.huasheng.enabled) {
            initPromises.push(this.initializeTradingService(appConfig.trading));
        } else if (!appConfig.trading.huasheng.enabled) {
            console.log("[ServiceManager] 交易服务已在配置中禁用");
            this.updateServiceStatus("trading", "disabled", false, "配置中已禁用");
        }

        // 立即广播初始状态，不等待服务初始化完成
        setImmediate(() => {
            this.emit("services-status-update", this.getServicesStatus());
        });

        // 等待所有服务初始化完成（允许部分失败）
        const results = await Promise.allSettled(initPromises);

        // 记录初始化结果
        let serviceIndex = 0;
        if (this.config.enableMarketData && appConfig.market.enabled) {
            if (results[serviceIndex] && results[serviceIndex].status === "rejected") {
                console.error(`[ServiceManager] marketData 服务初始化失败:`, results[serviceIndex]);
                this.updateServiceStatus("marketData", "error", false, (results[serviceIndex] as PromiseRejectedResult).reason?.message);
            }
            serviceIndex++;
        }
        if (this.config.enableTrading && appConfig.trading.huasheng.enabled) {
            if (results[serviceIndex] && results[serviceIndex].status === "rejected") {
                console.error(`[ServiceManager] trading 服务初始化失败:`, results[serviceIndex]);
                this.updateServiceStatus("trading", "error", false, (results[serviceIndex] as PromiseRejectedResult).reason?.message);
            }
        }

        this.isInitialized = true;
        console.log("[ServiceManager] 服务管理器初始化完成");
        this.emit("initialized", this.getServicesStatus());
    }

    /**
     * 更新服务配置状态
     */
    private updateServiceConfigStatus(appConfig: AppConfig): void {
        // 更新行情服务配置状态
        const marketService = this.services.get("marketData");
        if (marketService) {
            marketService.enabled = this.config.enableMarketData && appConfig.market.enabled;
            if (!marketService.enabled) {
                marketService.status = "disabled";
                marketService.connected = false;
                marketService.error = "配置中已禁用";
            }
        }

        // 更新交易服务配置状态
        const tradingService = this.services.get("trading");
        if (tradingService) {
            tradingService.enabled = this.config.enableTrading && appConfig.trading.huasheng.enabled;
            if (!tradingService.enabled) {
                tradingService.status = "disabled";
                tradingService.connected = false;
                tradingService.error = "配置中已禁用";
            }
        }
    }

    /**
     * 初始化行情数据服务
     */
    private async initializeMarketDataService(config: any): Promise<void> {
        try {
            console.log("[ServiceManager] 初始化行情数据服务...");
            this.updateServiceStatus("marketData", "initializing", false, "正在初始化行情服务...");

            this.marketDataManager = new MarketDataManager();

            // 设置事件监听
            this.marketDataManager.on("connected", () => {
                console.log("[ServiceManager] 行情数据服务连接成功");
                this.updateServiceStatus("marketData", "running", true);
                this.emit("service-connected", "marketData");
            });

            this.marketDataManager.on("disconnected", () => {
                console.log("[ServiceManager] 行情数据服务连接断开");
                this.updateServiceStatus("marketData", "error", false, "连接断开");
                this.emit("service-disconnected", "marketData");

                // 自动重连
                if (config.autoReconnect) {
                    this.scheduleReconnect("marketData", config.reconnectInterval);
                }
            });

            this.marketDataManager.on("connection-failed", (error) => {
                console.error("[ServiceManager] 行情数据服务连接失败:", error);
                this.updateServiceStatus("marketData", "error", false, error.message);
                this.emit("service-error", "marketData", error);

                // 自动重连（使用独立的异步处理，避免阻塞）
                if (config.autoReconnect) {
                    setImmediate(() => {
                        this.scheduleReconnect("marketData", config.reconnectInterval);
                    });
                }
            });

            // 尝试连接
            await this.marketDataManager.connect(config);
        } catch (error) {
            console.error("[ServiceManager] 行情数据服务初始化失败:", error);
            this.updateServiceStatus("marketData", "error", false, error instanceof Error ? error.message : "未知错误");
            throw error;
        }
    }

    /**
     * 初始化交易服务
     */
    private async initializeTradingService(config: any): Promise<void> {
        try {
            console.log("[ServiceManager] 初始化交易服务...");
            this.updateServiceStatus("trading", "initializing", false, "正在初始化交易服务...");

            this.tradingManager = new TradingManager(config);

            // 设置事件监听
            this.tradingManager.on("adapter-connected", (adapterName) => {
                console.log(`[ServiceManager] 交易适配器 ${adapterName} 连接成功`);
                this.updateServiceStatus("trading", "running", true);
                this.emit("service-connected", "trading");
            });

            this.tradingManager.on("adapter-disconnected", (adapterName, hadError) => {
                console.log(`[ServiceManager] 交易适配器 ${adapterName} 连接断开 (错误: ${hadError})`);
                this.updateServiceStatus("trading", "error", false, "连接断开");
                this.emit("service-disconnected", "trading");
            });

            this.tradingManager.on("adapter-error", (adapterName, error) => {
                console.error(`[ServiceManager] 交易适配器 ${adapterName} 错误:`, error);
                this.updateServiceStatus("trading", "error", false, error.message);
                this.emit("service-error", "trading", error);
            });

            // 异步连接华盛通适配器（不阻塞初始化）
            const huashengAdapter = this.tradingManager.getAdapter("huasheng");
            if (huashengAdapter) {
                // 使用setImmediate确保异步执行，不阻塞初始化
                setImmediate(async () => {
                    try {
                        await huashengAdapter.connect();
                    } catch (error) {
                        console.error("[ServiceManager] 华盛通适配器连接失败:", error);
                        // 错误已经通过事件监听器处理，这里不需要额外处理
                    }
                });
            }

            // 交易服务初始化完成（不依赖连接状态）
            console.log("[ServiceManager] 交易服务初始化完成");

            // 立即更新交易服务状态为运行中（即使连接尚未建立）
            this.updateServiceStatus("trading", "running", false, "服务已启动，正在连接...");
        } catch (error) {
            console.error("[ServiceManager] 交易服务初始化失败:", error);
            this.updateServiceStatus("trading", "error", false, error instanceof Error ? error.message : "未知错误");
            throw error;
        }
    }

    /**
     * 更新服务状态
     */
    private updateServiceStatus(serviceName: string, status: ServiceStatus["status"], connected: boolean, error?: string): void {
        const serviceStatus = this.services.get(serviceName);
        if (serviceStatus) {
            serviceStatus.status = status;
            serviceStatus.connected = connected;
            serviceStatus.error = error;
            serviceStatus.lastUpdate = Date.now();
            this.emit("service-status-changed", serviceName, serviceStatus);

            // 立即广播状态变化到前端（不等待其他服务）
            setImmediate(() => {
                this.broadcastStatusToRenderer(serviceName, serviceStatus);
            });
        }
    }

    /**
     * 广播状态到渲染进程
     */
    private broadcastStatusToRenderer(serviceName: string, serviceStatus: ServiceStatus): void {
        try {
            const mainWindow = WindowManager.getMainWindow();
            if (mainWindow && !mainWindow.isDestroyed()) {
                // 广播单个服务状态变化
                mainWindow.webContents.send("event:connectionStatus", {
                    service: serviceName,
                    status: serviceStatus.status,
                    connected: serviceStatus.connected,
                    enabled: serviceStatus.enabled,
                    error: serviceStatus.error,
                    timestamp: serviceStatus.lastUpdate
                });

                // 同时广播完整的系统状态
                mainWindow.webContents.send("event:systemStatus", {
                    services: this.getServicesStatus(),
                    timestamp: Date.now()
                });
            }
        } catch (error) {
            console.error("[ServiceManager] 广播状态失败:", error);
        }
    }

    /**
     * 安排重连
     */
    private scheduleReconnect(serviceName: string, delay: number): void {
        console.log(`[ServiceManager] ${delay}ms 后尝试重连 ${serviceName} 服务`);
        setTimeout(async () => {
            try {
                if (serviceName === "marketData" && this.marketDataManager) {
                    await this.marketDataManager.reconnect();
                } else if (serviceName === "trading" && this.tradingManager) {
                    const adapter = this.tradingManager.getAdapter("huasheng");
                    if (adapter) {
                        await adapter.connect();
                    }
                }
            } catch (error) {
                console.error(`[ServiceManager] ${serviceName} 重连失败:`, error);
            }
        }, delay);
    }

    /**
     * 获取所有服务状态
     */
    getServicesStatus(): Record<string, ServiceStatus> {
        const status: Record<string, ServiceStatus> = {};
        for (const [name, serviceStatus] of this.services) {
            status[name] = { ...serviceStatus };
        }
        return status;
    }

    /**
     * 获取特定服务
     */
    getMarketDataManager(): MarketDataManager | null {
        return this.marketDataManager;
    }

    getTradingManager(): TradingManager | null {
        return this.tradingManager;
    }

    /**
     * 检查服务是否可用
     */
    isServiceAvailable(serviceName: string): boolean {
        const service = this.services.get(serviceName);
        return service ? service.status === "running" && service.connected : false;
    }

    /**
     * 关闭所有服务
     */
    async shutdown(): Promise<void> {
        console.log("[ServiceManager] 开始关闭所有服务...");

        const shutdownPromises: Promise<void>[] = [];

        if (this.marketDataManager) {
            shutdownPromises.push(this.marketDataManager.disconnect());
        }

        if (this.tradingManager) {
            const adapters = this.tradingManager.getAllAdapters();
            for (const [name, adapter] of adapters) {
                shutdownPromises.push(adapter.disconnect());
            }
        }

        if (this.config.gracefulShutdown) {
            await Promise.allSettled(shutdownPromises);
        }

        this.isInitialized = false;
        console.log("[ServiceManager] 所有服务已关闭");
        this.emit("shutdown");
    }
}
