import { contextBridge, ipc<PERSON><PERSON><PERSON>, Ipc<PERSON>endererEvent } from "electron";

// 定义暴露给渲染进程的 API
const electronAPI = {
    // 交易系统相关
    tradingSystem: {
        getStatus: () => ipcRenderer.invoke("trading-system:getStatus"),

        initialize: (args?: any) => ipcRenderer.invoke("trading-system:initialize", args),

        shutdown: () => ipcRenderer.invoke("trading-system:shutdown"),

        getMarketStatus: () => ipcRenderer.invoke("trading-system:getMarketStatus"),

        getTradingStatus: () => ipcRenderer.invoke("trading-system:getTradingStatus")
    },

    // 任务管理
    task: {
        create: (taskConfig: any) => ipcRenderer.invoke("task:create", taskConfig),

        start: (args: { taskId: string }) => ipcRenderer.invoke("task:start", args),

        stop: (args: { taskId: string }) => ipcRenderer.invoke("task:stop", args),

        delete: (args: { taskId: string }) => ipcRenderer.invoke("task:delete", args),

        getList: () => ipcRenderer.invoke("task:getList"),

        getStatus: (args: { taskId: string }) => ipcRenderer.invoke("task:getStatus", args)
    },

    // 配置管理
    config: {
        get: (args: any) => ipcRenderer.invoke("config:get", args),

        save: (args: any) => ipcRenderer.invoke("config:save", args),

        update: (args: any) => ipcRenderer.invoke("config:update", args),

        getAll: () => ipcRenderer.invoke("config:getAll"),

        reload: () => ipcRenderer.invoke("config:reload"),

        validate: (args: any) => ipcRenderer.invoke("config:validate", args)
    },

    // 华盛通交易相关
    huasheng: {
        getAccountInfo: () => ipcRenderer.invoke("huasheng:getAccountInfo"),

        getFunds: () => ipcRenderer.invoke("huasheng:getFunds"),

        connect: () => ipcRenderer.invoke("huasheng:connect"),

        disconnect: () => ipcRenderer.invoke("huasheng:disconnect"),

        getConnectionStatus: () => ipcRenderer.invoke("huasheng:getConnectionStatus")
    },

    // 窗口控制
    window: {
        minimize: () => ipcRenderer.send("window:minimize"),
        maximize: () => ipcRenderer.send("window:maximize"),
        close: () => ipcRenderer.send("window:close"),
        toggleDevTools: () => ipcRenderer.send("window:toggleDevTools"),
        reload: () => ipcRenderer.send("window:reload")
    },

    // 右键菜单和编辑操作
    contextMenu: {
        showContextMenu: () => ipcRenderer.send("context-menu:show"),
        cut: () => ipcRenderer.invoke("edit:cut"),
        copy: () => ipcRenderer.invoke("edit:copy"),
        paste: () => ipcRenderer.invoke("edit:paste"),
        selectAll: () => ipcRenderer.invoke("edit:selectAll"),
        undo: () => ipcRenderer.invoke("edit:undo"),
        redo: () => ipcRenderer.invoke("edit:redo")
    },

    // 事件监听
    on: (channel: string, callback: (event: IpcRendererEvent, ...args: any[]) => void) => {
        // 验证通道白名单
        const validChannels = [
            "event:realtimeData",
            "event:connectionStatus",
            "event:systemStatus",
            "event:error",
            "trading-system:service-ready",
            "trading-system:service-shutdown",
            "trading-system:initialization-error",
            "trading-system:initialized",
            "trading-system:shutdown",
            "trading-system:heartbeat",
            "trading-system:task-created",
            "trading-system:task-started",
            "trading-system:task-stopped"
        ];

        console.log(`[Preload] 尝试监听事件: ${channel}`);

        if (validChannels.includes(channel)) {
            console.log(`[Preload] 事件通道验证通过: ${channel}`);
            ipcRenderer.on(channel, callback);
        } else {
            console.error(`[Preload] Invalid channel: ${channel}`);
        }
    },

    // 移除事件监听
    removeListener: (channel: string, callback: (...args: any[]) => void) => {
        ipcRenderer.removeListener(channel, callback);
    },

    // 移除所有事件监听
    removeAllListeners: (channel: string) => {
        ipcRenderer.removeAllListeners(channel);
    }
};

// 暴露 API 到渲染进程
contextBridge.exposeInMainWorld("electronAPI", electronAPI);

// 类型定义导出（供 TypeScript 使用）
export type ElectronAPI = typeof electronAPI;
