{"app": {"name": "量化交易终端", "version": "1.0.0", "environment": "development"}, "market": {"provider": "futu", "host": "127.0.0.1", "port": 33333, "encryptKey": "b7a5e91ae4fc24e7", "useHttps": false, "enabled": true, "optional": true, "autoReconnect": true, "reconnectInterval": 5000, "maxReconnectAttempts": 10}, "trading": {"huasheng": {"host": "***********", "port": 8080, "account": "***********", "password": "456789", "enabled": true, "heartbeatInterval": 10000, "reconnectInterval": 5000, "maxReconnectAttempts": 10, "commandTimeout": 30000}}, "system": {"enablePersistence": true, "logLevel": "debug", "taskExecutionInterval": 1000, "riskCheckInterval": 2000}, "logging": {"level": "info", "maxFiles": 5, "maxSize": "10MB"}, "riskControl": {"maxDailyLoss": 50000, "maxSingleOrderValue": 100000, "enableEmergencyStop": true}}